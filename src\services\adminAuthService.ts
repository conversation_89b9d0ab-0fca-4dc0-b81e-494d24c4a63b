/**
 * 🔐 ADMIN AUTHENTICATION SERVICE
 * Servizio per l'autenticazione e autorizzazione admin
 */

import { supabase } from "@/integrations/supabase/client";

/**
 * Configurazione admin
 */
const ADMIN_CONFIG = {
  // Email degli admin autorizzati (da configurare in produzione)
  authorizedEmails: [
    "<EMAIL>",
    "<EMAIL>",
  ],
  // Chiave segreta per accesso temporaneo (da cambiare in produzione)
  tempAccessKey: "maraffa-admin-2024",
  // Durata sessione admin (24 ore)
  sessionDuration: 24 * 60 * 60 * 1000,
  // Chiave localStorage per sessione admin
  sessionStorageKey: "maraffa-admin-session",
};

/**
 * Interfaccia per la sessione admin
 */
export interface AdminSession {
  isAdmin: boolean;
  email?: string;
  userId?: string;
  loginTime: number;
  expiresAt: number;
  accessLevel: "full" | "readonly";
}

/**
 * Classe per gestire l'autenticazione admin
 */
export class AdminAuthService {
  private currentSession: AdminSession | null = null;

  constructor() {
    this.loadSession();
  }

  /**
   * Carica la sessione dal localStorage
   */
  private loadSession(): void {
    try {
      const stored = localStorage.getItem(ADMIN_CONFIG.sessionStorageKey);
      if (stored) {
        const session: AdminSession = JSON.parse(stored);

        // Verifica se la sessione è ancora valida
        if (session.expiresAt > Date.now()) {
          this.currentSession = session;
          console.log("🔐 AdminAuth: Sessione admin caricata");
        } else {
          this.clearSession();
          console.log("🔐 AdminAuth: Sessione admin scaduta");
        }
      }
    } catch (error) {
      console.warn("⚠️ AdminAuth: Errore caricamento sessione:", error);
      this.clearSession();
    }
  }

  /**
   * Salva la sessione nel localStorage
   */
  private saveSession(): void {
    try {
      if (this.currentSession) {
        localStorage.setItem(
          ADMIN_CONFIG.sessionStorageKey,
          JSON.stringify(this.currentSession)
        );
      }
    } catch (error) {
      console.warn("⚠️ AdminAuth: Errore salvataggio sessione:", error);
    }
  }

  /**
   * Cancella la sessione
   */
  private clearSession(): void {
    this.currentSession = null;
    localStorage.removeItem(ADMIN_CONFIG.sessionStorageKey);
  }

  /**
   * Verifica se l'utente corrente è admin
   */
  public isAdmin(): boolean {
    return (
      this.currentSession?.isAdmin === true &&
      this.currentSession.expiresAt > Date.now()
    );
  }

  /**
   * Ottiene la sessione corrente
   */
  public getCurrentSession(): AdminSession | null {
    if (this.currentSession && this.currentSession.expiresAt <= Date.now()) {
      this.clearSession();
      return null;
    }
    return this.currentSession;
  }

  /**
   * Login con chiave temporanea (per sviluppo/testing)
   */
  public async loginWithTempKey(key: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      if (key === ADMIN_CONFIG.tempAccessKey) {
        const now = Date.now();
        this.currentSession = {
          isAdmin: true,
          email: "temp-admin@local",
          userId: "temp-admin",
          loginTime: now,
          expiresAt: now + ADMIN_CONFIG.sessionDuration,
          accessLevel: "full",
        };

        this.saveSession();
        console.log("✅ AdminAuth: Login temporaneo riuscito");
        return { success: true };
      } else {
        console.warn("⚠️ AdminAuth: Chiave temporanea non valida");
        return { success: false, error: "Chiave non valida" };
      }
    } catch (error) {
      console.error("❌ AdminAuth: Errore login temporaneo:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Errore sconosciuto",
      };
    }
  }

  /**
   * Login con Supabase (per produzione)
   */
  public async loginWithSupabase(): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();

      if (error || !user) {
        return { success: false, error: "Utente non autenticato" };
      }

      // Verifica se l'email è autorizzata
      if (!ADMIN_CONFIG.authorizedEmails.includes(user.email || "")) {
        console.warn("⚠️ AdminAuth: Email non autorizzata:", user.email);
        return { success: false, error: "Accesso non autorizzato" };
      }

      const now = Date.now();
      this.currentSession = {
        isAdmin: true,
        email: user.email || undefined,
        userId: user.id,
        loginTime: now,
        expiresAt: now + ADMIN_CONFIG.sessionDuration,
        accessLevel: "full",
      };

      this.saveSession();
      console.log("✅ AdminAuth: Login Supabase riuscito per:", user.email);
      return { success: true };
    } catch (error) {
      console.error("❌ AdminAuth: Errore login Supabase:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Errore sconosciuto",
      };
    }
  }

  /**
   * Logout
   */
  public logout(): void {
    console.log("🔐 AdminAuth: Logout admin");
    this.clearSession();
  }

  /**
   * Verifica permessi per una specifica azione
   */
  public hasPermission(
    action: "read" | "write" | "delete" | "export"
  ): boolean {
    if (!this.isAdmin()) {
      return false;
    }

    const session = this.getCurrentSession();
    if (!session) {
      return false;
    }

    // Admin con accesso completo può fare tutto
    if (session.accessLevel === "full") {
      return true;
    }

    // Admin readonly può solo leggere
    if (session.accessLevel === "readonly") {
      return action === "read";
    }

    return false;
  }

  /**
   * Ottiene informazioni sulla sessione per debug
   */
  public getSessionInfo(): {
    isLoggedIn: boolean;
    email?: string;
    accessLevel?: string;
    timeRemaining?: number;
  } {
    const session = this.getCurrentSession();

    if (!session) {
      return { isLoggedIn: false };
    }

    return {
      isLoggedIn: true,
      email: session.email,
      accessLevel: session.accessLevel,
      timeRemaining: Math.max(0, session.expiresAt - Date.now()),
    };
  }

  /**
   * Estende la sessione corrente
   */
  public extendSession(): boolean {
    if (!this.currentSession) {
      return false;
    }

    this.currentSession.expiresAt = Date.now() + ADMIN_CONFIG.sessionDuration;
    this.saveSession();
    console.log("🔐 AdminAuth: Sessione estesa");
    return true;
  }
}

/**
 * Istanza singleton del servizio
 */
export const adminAuth = new AdminAuthService();

// Import React per l'hook
import React from "react";

/**
 * Hook React per l'autenticazione admin
 */
export const useAdminAuth = () => {
  const [isAdmin, setIsAdmin] = React.useState(adminAuth.isAdmin());
  const [session, setSession] = React.useState(adminAuth.getCurrentSession());
  const [isLoading, setIsLoading] = React.useState(false);

  React.useEffect(() => {
    const checkAuth = () => {
      const currentIsAdmin = adminAuth.isAdmin();
      const currentSession = adminAuth.getCurrentSession();

      if (currentIsAdmin !== isAdmin) {
        setIsAdmin(currentIsAdmin);
      }

      if (currentSession !== session) {
        setSession(currentSession);
      }
    };

    // Controlla ogni minuto
    const interval = setInterval(checkAuth, 60000);

    // Controlla anche al mount
    checkAuth();

    return () => clearInterval(interval);
  }, [isAdmin, session]);

  const login = async (key?: string) => {
    setIsLoading(true);
    try {
      const result = key
        ? await adminAuth.loginWithTempKey(key)
        : await adminAuth.loginWithSupabase();

      if (result.success) {
        setIsAdmin(true);
        setSession(adminAuth.getCurrentSession());
      }

      return result;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    adminAuth.logout();
    setIsAdmin(false);
    setSession(null);
  };

  const refreshSession = () => {
    setIsAdmin(adminAuth.isAdmin());
    setSession(adminAuth.getCurrentSession());
  };

  return {
    isAdmin,
    session,
    isLoading,
    login,
    logout,
    refreshSession,
    hasPermission: adminAuth.hasPermission.bind(adminAuth),
    extendSession: adminAuth.extendSession.bind(adminAuth),
  };
};
