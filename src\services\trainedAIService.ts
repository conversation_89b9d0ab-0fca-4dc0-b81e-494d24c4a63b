/**
 * 🤖 TRAINED AI SERVICE
 * Servizio per gestire l'integrazione dell'AI addestrata nelle decisioni CPU
 */

import { GameState } from "@/utils/game/gameLogic";
import { Card, Suit } from "@/utils/game/cardUtils";
import type { AIDifficulty } from "@/utils/ai/types";

/**
 * Configurazione per l'AI addestrata
 */
export interface TrainedAIConfig {
  enabled: boolean;
  usagePercentage: number; // 0-100, percentuale di utilizzo dell'AI addestrata
  fallbackToTraditional: boolean; // Se fallire alla logica tradizionale in caso di errore
  minConfidenceThreshold: number; // 0-1, soglia minima di confidenza per usare la decisione AI
  enableLearning: boolean; // Se continuare ad apprendere durante il gioco
  debugMode: boolean; // Se loggare le decisioni AI per debug
}

/**
 * Risultato di una decisione AI
 */
export interface AIDecision {
  card: Card | null;
  confidence: number; // 0-1
  reasoning: string;
  usedTrainedAI: boolean;
  fallbackReason?: string;
}

/**
 * Statistiche dell'AI addestrata
 */
export interface TrainedAIStats {
  totalDecisions: number;
  trainedAIDecisions: number;
  traditionalFallbacks: number;
  averageConfidence: number;
  successRate: number; // Basato su feedback (se disponibile)
  lastUsed?: string;
}

/**
 * Classe principale per l'AI addestrata
 */
export class TrainedAIService {
  private config: TrainedAIConfig;
  private stats: TrainedAIStats;
  private readonly STORAGE_KEY = "trained-ai-config";
  private readonly STATS_KEY = "trained-ai-stats";

  constructor() {
    this.config = this.loadConfig();
    this.stats = this.loadStats();
  }

  /**
   * Carica la configurazione dal localStorage
   */
  private loadConfig(): TrainedAIConfig {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const config = JSON.parse(stored);
        return { ...this.getDefaultConfig(), ...config };
      }
    } catch (error) {
      console.warn("⚠️ TrainedAI: Errore caricamento config:", error);
    }
    
    return this.getDefaultConfig();
  }

  /**
   * Carica le statistiche dal localStorage
   */
  private loadStats(): TrainedAIStats {
    try {
      const stored = localStorage.getItem(this.STATS_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.warn("⚠️ TrainedAI: Errore caricamento stats:", error);
    }
    
    return {
      totalDecisions: 0,
      trainedAIDecisions: 0,
      traditionalFallbacks: 0,
      averageConfidence: 0,
      successRate: 0
    };
  }

  /**
   * Configurazione predefinita
   */
  private getDefaultConfig(): TrainedAIConfig {
    return {
      enabled: false, // Disabilitato di default
      usagePercentage: 50, // 50% delle decisioni
      fallbackToTraditional: true,
      minConfidenceThreshold: 0.6,
      enableLearning: false,
      debugMode: false
    };
  }

  /**
   * Salva la configurazione
   */
  private saveConfig(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.config));
    } catch (error) {
      console.warn("⚠️ TrainedAI: Errore salvataggio config:", error);
    }
  }

  /**
   * Salva le statistiche
   */
  private saveStats(): void {
    try {
      localStorage.setItem(this.STATS_KEY, JSON.stringify(this.stats));
    } catch (error) {
      console.warn("⚠️ TrainedAI: Errore salvataggio stats:", error);
    }
  }

  /**
   * Ottiene la configurazione corrente
   */
  public getConfig(): TrainedAIConfig {
    return { ...this.config };
  }

  /**
   * Aggiorna la configurazione
   */
  public updateConfig(newConfig: Partial<TrainedAIConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
    
    console.log("🤖 TrainedAI: Configurazione aggiornata", this.config);
  }

  /**
   * Ottiene le statistiche correnti
   */
  public getStats(): TrainedAIStats {
    return { ...this.stats };
  }

  /**
   * Reset delle statistiche
   */
  public resetStats(): void {
    this.stats = {
      totalDecisions: 0,
      trainedAIDecisions: 0,
      traditionalFallbacks: 0,
      averageConfidence: 0,
      successRate: 0
    };
    this.saveStats();
    console.log("🤖 TrainedAI: Statistiche resettate");
  }

  /**
   * Verifica se l'AI addestrata è abilitata e disponibile
   */
  public isAvailable(): boolean {
    return this.config.enabled;
  }

  /**
   * Decide se usare l'AI addestrata per questa decisione
   */
  private shouldUseTrainedAI(): boolean {
    if (!this.config.enabled) {
      return false;
    }

    // Usa la percentuale configurata per decidere
    const random = Math.random() * 100;
    return random < this.config.usagePercentage;
  }

  /**
   * Simula una decisione dell'AI addestrata
   * NOTA: Questa è una simulazione. In un'implementazione reale,
   * qui ci sarebbe il modello ML addestrato.
   */
  private simulateTrainedAIDecision(
    gameState: GameState,
    playerIndex: number,
    availableCards: Card[]
  ): AIDecision {
    // Simulazione: sceglie una carta casuale con confidenza variabile
    const randomCard = availableCards[Math.floor(Math.random() * availableCards.length)];
    const confidence = 0.4 + Math.random() * 0.6; // 0.4-1.0
    
    return {
      card: randomCard,
      confidence,
      reasoning: "Decisione basata su modello addestrato (simulazione)",
      usedTrainedAI: true
    };
  }

  /**
   * Ottiene una decisione AI (addestrata o tradizionale)
   */
  public async getAIDecision(
    gameState: GameState,
    playerIndex: number,
    availableCards: Card[],
    difficulty: AIDifficulty,
    traditionalAIFunction: (state: GameState, player: number, difficulty: AIDifficulty) => Card | null
  ): Promise<AIDecision> {
    this.stats.totalDecisions++;

    // Verifica se usare l'AI addestrata
    if (!this.shouldUseTrainedAI()) {
      const traditionalCard = traditionalAIFunction(gameState, playerIndex, difficulty);
      
      this.stats.traditionalFallbacks++;
      this.saveStats();

      return {
        card: traditionalCard,
        confidence: 0.8, // Confidenza fissa per AI tradizionale
        reasoning: "Decisione AI tradizionale",
        usedTrainedAI: false
      };
    }

    try {
      // Simula decisione AI addestrata
      const aiDecision = this.simulateTrainedAIDecision(gameState, playerIndex, availableCards);
      
      // Verifica soglia di confidenza
      if (aiDecision.confidence < this.config.minConfidenceThreshold) {
        if (this.config.fallbackToTraditional) {
          const traditionalCard = traditionalAIFunction(gameState, playerIndex, difficulty);
          
          this.stats.traditionalFallbacks++;
          this.saveStats();

          return {
            card: traditionalCard,
            confidence: 0.8,
            reasoning: "Fallback ad AI tradizionale (confidenza bassa)",
            usedTrainedAI: false,
            fallbackReason: `Confidenza ${aiDecision.confidence.toFixed(2)} sotto soglia ${this.config.minConfidenceThreshold}`
          };
        }
      }

      // Usa la decisione dell'AI addestrata
      this.stats.trainedAIDecisions++;
      this.stats.lastUsed = new Date().toISOString();
      
      // Aggiorna confidenza media
      this.stats.averageConfidence = 
        (this.stats.averageConfidence * (this.stats.trainedAIDecisions - 1) + aiDecision.confidence) / 
        this.stats.trainedAIDecisions;
      
      this.saveStats();

      if (this.config.debugMode) {
        console.log("🤖 TrainedAI: Decisione presa", {
          card: aiDecision.card?.displayName,
          confidence: aiDecision.confidence,
          reasoning: aiDecision.reasoning
        });
      }

      return aiDecision;

    } catch (error) {
      console.error("❌ TrainedAI: Errore decisione AI:", error);
      
      if (this.config.fallbackToTraditional) {
        const traditionalCard = traditionalAIFunction(gameState, playerIndex, difficulty);
        
        this.stats.traditionalFallbacks++;
        this.saveStats();

        return {
          card: traditionalCard,
          confidence: 0.8,
          reasoning: "Fallback ad AI tradizionale (errore)",
          usedTrainedAI: false,
          fallbackReason: `Errore AI addestrata: ${error}`
        };
      }

      return {
        card: null,
        confidence: 0,
        reasoning: "Errore AI addestrata, nessun fallback",
        usedTrainedAI: false,
        fallbackReason: `Errore: ${error}`
      };
    }
  }

  /**
   * Fornisce feedback su una decisione (per apprendimento futuro)
   */
  public provideFeedback(decision: AIDecision, wasSuccessful: boolean): void {
    if (!this.config.enableLearning) {
      return;
    }

    // In un'implementazione reale, qui si aggiornerebbe il modello
    console.log("🤖 TrainedAI: Feedback ricevuto", {
      usedTrainedAI: decision.usedTrainedAI,
      wasSuccessful,
      confidence: decision.confidence
    });

    // Aggiorna statistiche di successo (semplificato)
    // In realtà dovremmo tracciare meglio i successi/fallimenti
    if (decision.usedTrainedAI) {
      // Aggiorna success rate in modo semplificato
      const currentSuccesses = this.stats.successRate * this.stats.trainedAIDecisions / 100;
      const newSuccesses = currentSuccesses + (wasSuccessful ? 1 : 0);
      this.stats.successRate = (newSuccesses / this.stats.trainedAIDecisions) * 100;
      this.saveStats();
    }
  }

  /**
   * Ottiene informazioni di debug
   */
  public getDebugInfo(): {
    config: TrainedAIConfig;
    stats: TrainedAIStats;
    isAvailable: boolean;
    usageRate: number;
  } {
    const usageRate = this.stats.totalDecisions > 0 
      ? (this.stats.trainedAIDecisions / this.stats.totalDecisions) * 100 
      : 0;

    return {
      config: this.getConfig(),
      stats: this.getStats(),
      isAvailable: this.isAvailable(),
      usageRate
    };
  }
}

/**
 * Istanza singleton del servizio
 */
export const trainedAI = new TrainedAIService();
