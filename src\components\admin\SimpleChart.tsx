/**
 * 📈 SIMPLE CHART COMPONENT
 * Componente per visualizzare grafici semplici senza dipendenze esterne
 */

import React from "react";

interface DataPoint {
  label: string;
  value: number;
  color?: string;
}

interface SimpleBarChartProps {
  data: DataPoint[];
  height?: number;
  className?: string;
  showValues?: boolean;
}

export const SimpleBarChart: React.FC<SimpleBarChartProps> = ({
  data,
  height = 200,
  className = "",
  showValues = true
}) => {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height }}>
        <p className="text-gray-500">Nessun dato disponibile</p>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const barWidth = Math.max(40, (100 / data.length) - 2);

  return (
    <div className={`w-full ${className}`}>
      <div 
        className="relative flex items-end justify-around px-4 pb-8"
        style={{ height }}
      >
        {data.map((item, index) => {
          const barHeight = maxValue > 0 ? (item.value / maxValue) * (height - 60) : 0;
          const color = item.color || `hsl(${(index * 360) / data.length}, 70%, 50%)`;
          
          return (
            <div key={index} className="flex flex-col items-center">
              {/* Valore sopra la barra */}
              {showValues && (
                <div className="text-xs font-medium text-gray-700 mb-1">
                  {item.value}
                </div>
              )}
              
              {/* Barra */}
              <div
                className="rounded-t-md transition-all duration-300 hover:opacity-80"
                style={{
                  width: `${barWidth}px`,
                  height: `${barHeight}px`,
                  backgroundColor: color,
                  minHeight: item.value > 0 ? "4px" : "0px"
                }}
              />
              
              {/* Label sotto la barra */}
              <div className="text-xs text-gray-600 mt-2 text-center max-w-16 break-words">
                {item.label}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

interface SimpleLineChartProps {
  data: DataPoint[];
  height?: number;
  className?: string;
  color?: string;
}

export const SimpleLineChart: React.FC<SimpleLineChartProps> = ({
  data,
  height = 200,
  className = "",
  color = "#3b82f6"
}) => {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height }}>
        <p className="text-gray-500">Nessun dato disponibile</p>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const range = maxValue - minValue || 1;
  
  const chartHeight = height - 60;
  const chartWidth = 100; // Percentuale

  // Calcola i punti per la linea SVG
  const points = data.map((item, index) => {
    const x = (index / (data.length - 1)) * chartWidth;
    const y = chartHeight - ((item.value - minValue) / range) * chartHeight;
    return `${x},${y}`;
  }).join(' ');

  return (
    <div className={`w-full ${className}`}>
      <div className="relative" style={{ height }}>
        {/* SVG per la linea */}
        <svg 
          className="absolute inset-0 w-full h-full"
          viewBox={`0 0 ${chartWidth} ${height}`}
          preserveAspectRatio="none"
        >
          {/* Griglia di sfondo */}
          <defs>
            <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
              <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#f3f4f6" strokeWidth="0.5"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          {/* Area sotto la linea */}
          <polygon
            points={`0,${chartHeight} ${points} ${chartWidth},${chartHeight}`}
            fill={color}
            fillOpacity="0.1"
          />
          
          {/* Linea principale */}
          <polyline
            points={points}
            fill="none"
            stroke={color}
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Punti */}
          {data.map((item, index) => {
            const x = (index / (data.length - 1)) * chartWidth;
            const y = chartHeight - ((item.value - minValue) / range) * chartHeight;
            
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="3"
                fill={color}
                className="hover:r-4 transition-all"
              />
            );
          })}
        </svg>
        
        {/* Labels */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-between px-2">
          {data.map((item, index) => (
            <div key={index} className="text-xs text-gray-600 text-center">
              {item.label}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

interface SimpleProgressRingProps {
  value: number;
  max: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  showValue?: boolean;
  label?: string;
}

export const SimpleProgressRing: React.FC<SimpleProgressRingProps> = ({
  value,
  max,
  size = 120,
  strokeWidth = 8,
  color = "#3b82f6",
  backgroundColor = "#e5e7eb",
  showValue = true,
  label
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const percentage = Math.min((value / max) * 100, 100);
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className="flex flex-col items-center">
      <div className="relative" style={{ width: size, height: size }}>
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
        >
          {/* Cerchio di sfondo */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={backgroundColor}
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          
          {/* Cerchio di progresso */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-500 ease-in-out"
          />
        </svg>
        
        {/* Valore al centro */}
        {showValue && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-lg font-bold text-gray-900">
                {Math.round(percentage)}%
              </div>
              {label && (
                <div className="text-xs text-gray-600">
                  {label}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
