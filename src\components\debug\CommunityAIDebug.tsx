/**
 * 🧠 COMMUNITY AI DEBUG COMPONENT
 * Componente per testare e debuggare il sistema Community AI
 */

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Database,
  Upload,
  BarChart3,
  Settings,
} from "lucide-react";

import {
  communityAI,
  getCommunityAIStats,
} from "@/services/communityAIService";
import { gameDataCollector } from "@/services/gameDataCollector";
import {
  setupCommunityAIStorage,
  testAITrainingUpload,
  debugBucketInfo,
  checkAITrainingBucket,
} from "@/utils/supabase/storageSetup";
import {
  runCommunityAIIntegrationTest,
  runPerformanceTest,
} from "@/utils/testing/communityAITest";
import { DataFlowTest } from "@/components/admin/DataFlowTest";
import type { CommunityAIStats } from "@/types/communityAI";

interface TestResult {
  name: string;
  status: "success" | "error" | "warning" | "pending";
  message: string;
  details?: any;
}

export const CommunityAIDebug: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [stats, setStats] = useState<CommunityAIStats | null>(null);
  const [collectorStats, setCollectorStats] = useState<any>(null);

  // Carica statistiche iniziali
  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = () => {
    setStats(getCommunityAIStats());
    setCollectorStats(gameDataCollector.getCollectionStats());
  };

  const runAllTests = async () => {
    setIsLoading(true);
    setTestResults([]);

    const results: TestResult[] = [];

    try {
      // Test 1: Verifica configurazione servizio
      results.push({
        name: "Configurazione Servizio",
        status: communityAI.isEnabled() ? "success" : "warning",
        message: communityAI.isEnabled()
          ? "Servizio abilitato"
          : "Servizio disabilitato",
      });

      // Test 2: Inizializzazione servizio
      results.push({
        name: "Inizializzazione",
        status: "pending",
        message: "Inizializzando servizio...",
      });
      setTestResults([...results]);

      const initResult = await communityAI.initialize();
      results[results.length - 1] = {
        name: "Inizializzazione",
        status: initResult.success ? "success" : "error",
        message: initResult.success
          ? "Servizio inizializzato"
          : `Errore: ${initResult.error}`,
        details: initResult,
      };
      setTestResults([...results]);

      // Test 3: Verifica bucket
      results.push({
        name: "Verifica Bucket",
        status: "pending",
        message: "Verificando bucket Supabase...",
      });
      setTestResults([...results]);

      const bucketCheck = await checkAITrainingBucket();
      results[results.length - 1] = {
        name: "Verifica Bucket",
        status: bucketCheck.exists ? "success" : "error",
        message: bucketCheck.exists
          ? "Bucket trovato"
          : `Bucket non trovato: ${bucketCheck.error}`,
        details: bucketCheck,
      };
      setTestResults([...results]);

      // Test 4: Test upload
      if (bucketCheck.exists) {
        results.push({
          name: "Test Upload",
          status: "pending",
          message: "Testando upload file...",
        });
        setTestResults([...results]);

        const uploadTest = await testAITrainingUpload();
        results[results.length - 1] = {
          name: "Test Upload",
          status: uploadTest.success ? "success" : "error",
          message: uploadTest.success
            ? `Upload riuscito (${uploadTest.fileSize} bytes)`
            : `Errore upload: ${uploadTest.error}`,
          details: uploadTest,
        };
        setTestResults([...results]);
      }

      // Test 5: Setup completo
      results.push({
        name: "Setup Completo",
        status: "pending",
        message: "Eseguendo setup completo...",
      });
      setTestResults([...results]);

      const setupResult = await setupCommunityAIStorage();
      results[results.length - 1] = {
        name: "Setup Completo",
        status: setupResult.success ? "success" : "error",
        message: setupResult.success
          ? "Setup completato con successo"
          : `Errori: ${setupResult.errors.join(", ")}`,
        details: setupResult,
      };
      setTestResults([...results]);
    } catch (error) {
      results.push({
        name: "Errore Generale",
        status: "error",
        message: error instanceof Error ? error.message : "Errore sconosciuto",
        details: error,
      });
    }

    setTestResults(results);
    setIsLoading(false);
    loadStats();
  };

  const resetStats = () => {
    communityAI.resetStats();
    gameDataCollector.reset();
    loadStats();
  };

  const debugInfo = async () => {
    console.log("🔍 Community AI Debug Info:");
    await debugBucketInfo();
    console.log("Stats:", getCommunityAIStats());
    console.log("Collector:", gameDataCollector.getCollectionStats());
  };

  const runIntegrationTest = async () => {
    setIsLoading(true);
    try {
      console.log("🧪 Eseguendo test di integrazione...");
      const result = await runCommunityAIIntegrationTest();

      const testResult: TestResult = {
        name: "Test Integrazione Completo",
        status: result.success ? "success" : "error",
        message: result.success
          ? "Tutti i test di integrazione sono passati"
          : `Errori: ${result.errors.join(", ")}`,
        details: result,
      };

      setTestResults([testResult]);
      loadStats();
    } catch (error) {
      setTestResults([
        {
          name: "Test Integrazione",
          status: "error",
          message:
            error instanceof Error ? error.message : "Errore sconosciuto",
          details: error,
        },
      ]);
    }
    setIsLoading(false);
  };

  const runPerfTest = async () => {
    setIsLoading(true);
    try {
      console.log("⚡ Eseguendo test di performance...");
      const result = await runPerformanceTest();

      const testResult: TestResult = {
        name: "Test Performance",
        status: result.success ? "success" : "warning",
        message: result.success
          ? `Performance OK - Raccolta: ${result.metrics.collectionOverhead.toFixed(
              1
            )}ms, Upload: ${result.metrics.uploadTime.toFixed(0)}ms`
          : `Performance sotto soglia - ${result.errors.join(", ")}`,
        details: result,
      };

      setTestResults([testResult]);
    } catch (error) {
      setTestResults([
        {
          name: "Test Performance",
          status: "error",
          message:
            error instanceof Error ? error.message : "Errore sconosciuto",
          details: error,
        },
      ]);
    }
    setIsLoading(false);
  };

  const getStatusIcon = (status: TestResult["status"]) => {
    switch (status) {
      case "success":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "error":
        return <XCircle className="w-4 h-4 text-red-500" />;
      case "warning":
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case "pending":
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
    }
  };

  const getStatusBadge = (status: TestResult["status"]) => {
    const variants = {
      success: "default",
      error: "destructive",
      warning: "secondary",
      pending: "outline",
    } as const;

    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">🧠 Community AI Debug</h2>
        <div className="flex gap-2">
          <Button onClick={debugInfo} variant="outline" size="sm">
            <Settings className="w-4 h-4 mr-2" />
            Debug Console
          </Button>
          <Button onClick={resetStats} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset Stats
          </Button>
          <Button
            onClick={runIntegrationTest}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            🧪 Test Integrazione
          </Button>
          <Button
            onClick={runPerfTest}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            ⚡ Test Performance
          </Button>
          <Button onClick={runAllTests} disabled={isLoading}>
            {isLoading ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Database className="w-4 h-4 mr-2" />
            )}
            Test Completo
          </Button>
        </div>
      </div>

      {/* Statistiche */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Statistiche Servizio
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {stats ? (
              <>
                <div className="flex justify-between">
                  <span>Partite raccolte:</span>
                  <Badge>{stats.totalGamesCollected}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Upload riusciti:</span>
                  <Badge variant="default">{stats.successfulUploads}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Errori upload:</span>
                  <Badge variant="destructive">{stats.uploadErrors}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Dati inviati:</span>
                  <Badge>{stats.totalDataUploaded} KB</Badge>
                </div>
                {stats.lastSuccessfulUpload && (
                  <div className="text-sm text-muted-foreground">
                    Ultimo upload:{" "}
                    {new Date(stats.lastSuccessfulUpload).toLocaleString()}
                  </div>
                )}
              </>
            ) : (
              <div className="text-muted-foreground">
                Nessuna statistica disponibile
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              Stato Collector
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {collectorStats ? (
              <>
                <div className="flex justify-between">
                  <span>Attivo:</span>
                  <Badge
                    variant={collectorStats.isActive ? "default" : "secondary"}
                  >
                    {collectorStats.isActive ? "Sì" : "No"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Mosse registrate:</span>
                  <Badge>{collectorStats.movesRecorded}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Trick stimati:</span>
                  <Badge>{collectorStats.estimatedTricks}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Tempo trascorso:</span>
                  <Badge>
                    {Math.round(collectorStats.elapsedTime / 1000)}s
                  </Badge>
                </div>
              </>
            ) : (
              <div className="text-muted-foreground">
                Collector non inizializzato
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Risultati Test */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Risultati Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <div className="font-medium">{result.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {result.message}
                      </div>
                    </div>
                  </div>
                  {getStatusBadge(result.status)}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Flusso Completo */}
      <DataFlowTest />
    </div>
  );
};
