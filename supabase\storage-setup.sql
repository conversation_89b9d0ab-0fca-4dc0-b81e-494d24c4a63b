-- 🧠 COMMUNITY AI - Configurazione Storage Supabase
-- Script per creare il bucket e le policy per i dati di training AI

-- STEP 1: Crea il bucket manualmente nel dashboard Supabase
-- Nome: ai-training-data
-- Public: false
-- File size limit: 102400 (100KB)
-- Allowed MIME types: application/json,application/gzip,text/plain

-- STEP 2: Esegui questo script SQL per le policy

-- Prima elimina eventuali policy esistenti (ignora errori se non esistono)
DROP POLICY IF EXISTS "Allow anonymous uploads to ai-training-data" ON storage.objects;
DROP POLICY IF EXISTS "Allow anonymous read access to ai-training-data" ON storage.objects;
DROP POLICY IF EXISTS "Allow delete access to ai-training-data" ON storage.objects;

-- Policy per permettere inserimenti anonimi (upload dati)
CREATE POLICY "Allow anonymous uploads to ai-training-data"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'ai-training-data'
);

-- Policy per permettere lettura anonima (per debug e admin)
CREATE POLICY "Allow anonymous read access to ai-training-data"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'ai-training-data'
);

-- Policy per permettere eliminazione (per pulizia dati)
CREATE POLICY "Allow delete access to ai-training-data"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'ai-training-data'
);

-- Crea una tabella per tracciare le statistiche di upload
CREATE TABLE IF NOT EXISTS public.ai_training_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  file_count INTEGER DEFAULT 0,
  total_size_kb INTEGER DEFAULT 0,
  last_upload TIMESTAMP WITH TIME ZONE,
  version TEXT DEFAULT '1.0',
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inserisci record iniziale per le statistiche
INSERT INTO public.ai_training_stats (file_count, total_size_kb)
VALUES (0, 0)
ON CONFLICT DO NOTHING;

-- RLS per la tabella statistiche
ALTER TABLE public.ai_training_stats ENABLE ROW LEVEL SECURITY;

-- Elimina eventuali policy esistenti per la tabella (ora che esiste)
DROP POLICY IF EXISTS "Allow public read access to ai_training_stats" ON public.ai_training_stats;
DROP POLICY IF EXISTS "Allow public updates to ai_training_stats" ON public.ai_training_stats;
DROP POLICY IF EXISTS "Allow public insert to ai_training_stats" ON public.ai_training_stats;

-- Policy per lettura pubblica delle statistiche
CREATE POLICY "Allow public read access to ai_training_stats"
ON public.ai_training_stats
FOR SELECT
USING (true);

-- Policy per aggiornamento delle statistiche (pubblico per semplicità)
CREATE POLICY "Allow public updates to ai_training_stats"
ON public.ai_training_stats
FOR UPDATE
USING (true)
WITH CHECK (true);

-- Policy per inserimento delle statistiche
CREATE POLICY "Allow public insert to ai_training_stats"
ON public.ai_training_stats
FOR INSERT
WITH CHECK (true);

-- Funzione per aggiornare automaticamente updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger per aggiornare automaticamente updated_at
DROP TRIGGER IF EXISTS update_ai_training_stats_updated_at ON public.ai_training_stats;
CREATE TRIGGER update_ai_training_stats_updated_at
    BEFORE UPDATE ON public.ai_training_stats
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
