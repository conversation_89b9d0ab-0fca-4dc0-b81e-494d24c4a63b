-- 🧠 COMMUNITY AI - Configurazione Storage Supabase
-- Script per creare il bucket per i dati di training AI

-- Crea il bucket per i dati di training AI
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'ai-training-data',
  'ai-training-data',
  false, -- Non pubblico per privacy
  102400, -- 100KB limite per file
  ARRAY['application/json', 'application/gzip', 'text/plain']
)
ON CONFLICT (id) DO NOTHING;

-- Policy per permettere inserimenti anonimi (solo INSERT)
-- Questo permette all'app di caricare dati senza autenticazione
CREATE POLICY "Allow anonymous uploads to ai-training-data"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'ai-training-data' AND
  -- <PERSON>ita la dimensione del file
  (storage.foldername(name))[1] = 'game_data' AND
  -- Solo file con estensione .json.gz
  name ~ '\.json\.gz$'
);

-- Policy per permettere solo agli admin di leggere i dati
-- (opzionale, per future analisi)
CREATE POLICY "Allow admin read access to ai-training-data"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'ai-training-data' AND
  auth.jwt() ->> 'role' = 'admin'
);

-- Crea una tabella per tracciare le statistiche di upload (opzionale)
CREATE TABLE IF NOT EXISTS public.ai_training_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  file_count INTEGER DEFAULT 0,
  total_size_kb INTEGER DEFAULT 0,
  last_upload TIMESTAMP WITH TIME ZONE,
  version TEXT DEFAULT '1.0'
);

-- Inserisci record iniziale per le statistiche
INSERT INTO public.ai_training_stats (file_count, total_size_kb)
VALUES (0, 0)
ON CONFLICT DO NOTHING;

-- RLS per la tabella statistiche (solo lettura pubblica)
ALTER TABLE public.ai_training_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow public read access to ai_training_stats"
ON public.ai_training_stats
FOR SELECT
USING (true);

-- Policy per aggiornare le statistiche (solo sistema)
CREATE POLICY "Allow system updates to ai_training_stats"
ON public.ai_training_stats
FOR UPDATE
USING (true)
WITH CHECK (true);
