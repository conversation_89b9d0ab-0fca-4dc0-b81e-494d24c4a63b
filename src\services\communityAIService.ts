/**
 * 🧠 COMMUNITY AI SERVICE
 * Servizio per raccogliere, comprimere e inviare dati delle partite per il training AI
 */

import { supabase } from "@/integrations/supabase/client";
import {
  GameTrainingData,
  CompressedGameData,
  CommunityAIConfig,
  UploadResult,
  CommunityAIStats,
} from "@/types/communityAI";
import { setupCommunityAIStorage } from "@/utils/supabase/storageSetup";

/**
 * Configurazione predefinita del servizio
 */
const DEFAULT_CONFIG: CommunityAIConfig = {
  enabled: true,
  maxCompressedSize: 100, // 100KB max
  maxRetries: 3,
  uploadTimeout: 10000, // 10 secondi
  supabaseBucket: "ai-training-data",
};

/**
 * Chiave per le statistiche nel localStorage
 */
const STATS_STORAGE_KEY = "community-ai-stats";

/**
 * Classe principale per il servizio Community AI
 */
export class CommunityAIService {
  private config: CommunityAIConfig;
  private stats: CommunityAIStats;

  constructor(config: Partial<CommunityAIConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.stats = this.loadStats();
  }

  /**
   * Carica le statistiche dal localStorage
   */
  private loadStats(): CommunityAIStats {
    try {
      const stored = localStorage.getItem(STATS_STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.warn("⚠️ CommunityAI: Errore caricamento statistiche:", error);
    }

    // Statistiche predefinite
    return {
      totalGamesCollected: 0,
      successfulUploads: 0,
      uploadErrors: 0,
      totalDataUploaded: 0,
    };
  }

  /**
   * Salva le statistiche nel localStorage
   */
  private saveStats(): void {
    try {
      localStorage.setItem(STATS_STORAGE_KEY, JSON.stringify(this.stats));
    } catch (error) {
      console.warn("⚠️ CommunityAI: Errore salvataggio statistiche:", error);
    }
  }

  /**
   * Comprimi i dati di gioco usando gzip (simulato con JSON.stringify + base64)
   * Nota: In un ambiente reale si userebbe pako o una libreria simile per gzip
   */
  private compressGameData(data: GameTrainingData): CompressedGameData {
    const jsonString = JSON.stringify(data);
    const originalSize = new Blob([jsonString]).size;

    // Simulazione compressione (in realtà solo base64)
    // In produzione usare: pako.gzip(jsonString) poi base64
    const compressedData = btoa(jsonString);
    const compressedSize = new Blob([compressedData]).size;

    return {
      id: this.generateFileId(),
      timestamp: Date.now(),
      compressedData,
      originalSize,
      compressedSize,
      version: data.version,
    };
  }

  /**
   * Genera un ID univoco per il file
   */
  private generateFileId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `game_${timestamp}_${random}`;
  }

  /**
   * Verifica se il servizio è abilitato e configurato correttamente
   */
  public isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * Inizializza il servizio e verifica la configurazione Supabase
   */
  public async initialize(): Promise<{
    success: boolean;
    error?: string;
  }> {
    if (!this.isEnabled()) {
      return { success: false, error: "Servizio disabilitato" };
    }

    try {
      console.log("🧠 CommunityAI: Inizializzazione servizio...");

      const setupResult = await setupCommunityAIStorage();

      if (setupResult.success) {
        console.log("✅ CommunityAI: Servizio inizializzato correttamente");
        return { success: true };
      } else {
        const errorMessage = setupResult.errors.join(", ");
        console.warn(
          "⚠️ CommunityAI: Errori durante inizializzazione:",
          errorMessage
        );
        return { success: false, error: errorMessage };
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Errore sconosciuto";
      console.error("❌ CommunityAI: Errore inizializzazione:", errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Invia i dati compressi a Supabase
   */
  private async uploadToSupabase(
    compressedData: CompressedGameData
  ): Promise<UploadResult> {
    const startTime = Date.now();

    try {
      // Verifica dimensione
      if (
        compressedData.compressedSize >
        this.config.maxCompressedSize * 1024
      ) {
        throw new Error(
          `File troppo grande: ${compressedData.compressedSize} bytes`
        );
      }

      const fileName = `${compressedData.id}.json.gz`;

      // Upload al bucket Supabase Storage
      const { data, error } = await supabase.storage
        .from(this.config.supabaseBucket)
        .upload(fileName, compressedData.compressedData, {
          contentType: "application/json",
          metadata: {
            version: compressedData.version,
            originalSize: compressedData.originalSize.toString(),
            compressedSize: compressedData.compressedSize.toString(),
          },
        });

      if (error) {
        throw error;
      }

      const uploadTime = Date.now() - startTime;

      return {
        success: true,
        fileId: compressedData.id,
        fileSize: compressedData.compressedSize,
        uploadTime,
      };
    } catch (error) {
      const uploadTime = Date.now() - startTime;

      return {
        success: false,
        error: error instanceof Error ? error.message : "Errore sconosciuto",
        uploadTime,
      };
    }
  }

  /**
   * Raccoglie e invia i dati di una partita completata
   */
  public async collectGameData(
    gameData: GameTrainingData
  ): Promise<UploadResult> {
    console.log("🧠 CommunityAI: Inizio raccolta dati partita...");

    // Verifica se il servizio è abilitato
    if (!this.isEnabled()) {
      console.log("🧠 CommunityAI: Servizio disabilitato");
      return {
        success: false,
        error: "Servizio disabilitato",
      };
    }

    try {
      // Aggiorna statistiche
      this.stats.totalGamesCollected++;

      // Comprimi i dati
      const compressedData = this.compressGameData(gameData);

      console.log("🧠 CommunityAI: Dati compressi", {
        originalSize: `${Math.round(compressedData.originalSize / 1024)}KB`,
        compressedSize: `${Math.round(compressedData.compressedSize / 1024)}KB`,
        compressionRatio: `${Math.round(
          (1 - compressedData.compressedSize / compressedData.originalSize) *
            100
        )}%`,
      });

      // Invia a Supabase con retry
      let lastError: string = "";

      for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
        console.log(
          `🧠 CommunityAI: Tentativo invio ${attempt}/${this.config.maxRetries}`
        );

        const result = await this.uploadToSupabase(compressedData);

        if (result.success) {
          // Aggiorna statistiche successo
          this.stats.successfulUploads++;
          this.stats.totalDataUploaded += Math.round(
            compressedData.compressedSize / 1024
          );
          this.stats.lastSuccessfulUpload = new Date().toISOString();
          this.saveStats();

          console.log("✅ CommunityAI: Dati inviati con successo", {
            fileId: result.fileId,
            fileSize: `${Math.round((result.fileSize || 0) / 1024)}KB`,
            uploadTime: `${result.uploadTime}ms`,
          });

          return result;
        }

        lastError = result.error || "Errore sconosciuto";
        console.warn(
          `⚠️ CommunityAI: Tentativo ${attempt} fallito:`,
          lastError
        );

        // Attendi prima del prossimo tentativo (eccetto l'ultimo)
        if (attempt < this.config.maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
        }
      }

      // Tutti i tentativi falliti
      this.stats.uploadErrors++;
      this.stats.lastError = new Date().toISOString();
      this.saveStats();

      console.error(
        "❌ CommunityAI: Invio fallito dopo tutti i tentativi:",
        lastError
      );

      return {
        success: false,
        error: lastError,
      };
    } catch (error) {
      this.stats.uploadErrors++;
      this.stats.lastError = new Date().toISOString();
      this.saveStats();

      const errorMessage =
        error instanceof Error ? error.message : "Errore sconosciuto";
      console.error(
        "❌ CommunityAI: Errore durante raccolta dati:",
        errorMessage
      );

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Ottieni le statistiche del servizio
   */
  public getStats(): CommunityAIStats {
    return { ...this.stats };
  }

  /**
   * Reset delle statistiche (per debug/testing)
   */
  public resetStats(): void {
    this.stats = {
      totalGamesCollected: 0,
      successfulUploads: 0,
      uploadErrors: 0,
      totalDataUploaded: 0,
    };
    this.saveStats();
    console.log("🧠 CommunityAI: Statistiche resettate");
  }

  /**
   * Aggiorna la configurazione
   */
  public updateConfig(newConfig: Partial<CommunityAIConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log("🧠 CommunityAI: Configurazione aggiornata", this.config);
  }
}

/**
 * Istanza singleton del servizio
 */
export const communityAI = new CommunityAIService();

/**
 * Funzione di utilità per raccogliere dati di una partita
 */
export const collectGameData = (
  gameData: GameTrainingData
): Promise<UploadResult> => {
  return communityAI.collectGameData(gameData);
};

/**
 * Funzione di utilità per ottenere le statistiche
 */
export const getCommunityAIStats = (): CommunityAIStats => {
  return communityAI.getStats();
};
