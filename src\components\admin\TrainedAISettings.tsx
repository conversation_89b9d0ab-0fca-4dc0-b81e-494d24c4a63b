/**
 * 🤖 TRAINED AI SETTINGS COMPONENT
 * Componente per configurare l'AI addestrata
 */

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  Brain,
  Settings,
  BarChart3,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Activity,
  Zap,
  Target,
  TrendingUp
} from "lucide-react";

import { 
  trainedAI,
  type TrainedAIConfig,
  type TrainedAIStats
} from "@/services/trainedAIService";

export const TrainedAISettings: React.FC = () => {
  const [config, setConfig] = useState<TrainedAIConfig>(trainedAI.getConfig());
  const [stats, setStats] = useState<TrainedAIStats>(trainedAI.getStats());
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setConfig(trainedAI.getConfig());
    setStats(trainedAI.getStats());
    setHasChanges(false);
  };

  const handleConfigChange = (key: keyof TrainedAIConfig, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    setHasChanges(true);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      trainedAI.updateConfig(config);
      setHasChanges(false);
      console.log("✅ Configurazione AI salvata");
    } catch (error) {
      console.error("❌ Errore salvataggio configurazione:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    loadData();
  };

  const handleResetStats = () => {
    trainedAI.resetStats();
    setStats(trainedAI.getStats());
  };

  const getUsageRate = (): number => {
    return stats.totalDecisions > 0 
      ? (stats.trainedAIDecisions / stats.totalDecisions) * 100 
      : 0;
  };

  const getSuccessColor = (rate: number): string => {
    if (rate >= 70) return "text-green-600";
    if (rate >= 50) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Brain className="w-8 h-8 text-purple-600" />
          <div>
            <h2 className="text-2xl font-bold">Configurazione AI Addestrata</h2>
            <p className="text-gray-600">
              Gestisci l'integrazione dell'AI addestrata nelle decisioni CPU
            </p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          <Button
            onClick={handleResetStats}
            variant="outline"
            size="sm"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset Stats
          </Button>
          {hasChanges && (
            <>
              <Button
                onClick={handleReset}
                variant="outline"
                size="sm"
              >
                Annulla
              </Button>
              <Button
                onClick={handleSave}
                disabled={isSaving}
                size="sm"
              >
                {isSaving ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="w-4 h-4 mr-2" />
                )}
                Salva
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Stato Attuale */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Stato</p>
                <p className="text-lg font-bold">
                  {config.enabled ? "Attiva" : "Disattiva"}
                </p>
              </div>
              <div className={`w-3 h-3 rounded-full ${config.enabled ? 'bg-green-500' : 'bg-gray-400'}`} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Decisioni Totali</p>
                <p className="text-lg font-bold">{stats.totalDecisions}</p>
              </div>
              <Activity className="w-6 h-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Uso AI</p>
                <p className="text-lg font-bold">{getUsageRate().toFixed(1)}%</p>
              </div>
              <Zap className="w-6 h-6 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Successo</p>
                <p className={`text-lg font-bold ${getSuccessColor(stats.successRate)}`}>
                  {stats.successRate.toFixed(1)}%
                </p>
              </div>
              <Target className="w-6 h-6 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configurazione */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Configurazione Principale
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Abilita AI */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Abilita AI Addestrata</Label>
                <p className="text-sm text-gray-600">
                  Attiva l'uso dell'AI addestrata nelle decisioni CPU
                </p>
              </div>
              <Switch
                checked={config.enabled}
                onCheckedChange={(checked) => handleConfigChange('enabled', checked)}
              />
            </div>

            <Separator />

            {/* Percentuale Utilizzo */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Percentuale Utilizzo</Label>
                <Badge variant="outline">{config.usagePercentage}%</Badge>
              </div>
              <Slider
                value={[config.usagePercentage]}
                onValueChange={(value) => handleConfigChange('usagePercentage', value[0])}
                max={100}
                step={5}
                disabled={!config.enabled}
                className="w-full"
              />
              <p className="text-sm text-gray-600">
                Percentuale di decisioni che useranno l'AI addestrata
              </p>
            </div>

            <Separator />

            {/* Soglia Confidenza */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Soglia Confidenza</Label>
                <Badge variant="outline">{config.minConfidenceThreshold.toFixed(2)}</Badge>
              </div>
              <Slider
                value={[config.minConfidenceThreshold * 100]}
                onValueChange={(value) => handleConfigChange('minConfidenceThreshold', value[0] / 100)}
                max={100}
                step={5}
                disabled={!config.enabled}
                className="w-full"
              />
              <p className="text-sm text-gray-600">
                Confidenza minima richiesta per usare la decisione AI
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Opzioni Avanzate
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Fallback */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Fallback AI Tradizionale</Label>
                <p className="text-sm text-gray-600">
                  Usa AI tradizionale se quella addestrata fallisce
                </p>
              </div>
              <Switch
                checked={config.fallbackToTraditional}
                onCheckedChange={(checked) => handleConfigChange('fallbackToTraditional', checked)}
                disabled={!config.enabled}
              />
            </div>

            <Separator />

            {/* Apprendimento */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Apprendimento Continuo</Label>
                <p className="text-sm text-gray-600">
                  Continua ad apprendere durante il gioco
                </p>
              </div>
              <Switch
                checked={config.enableLearning}
                onCheckedChange={(checked) => handleConfigChange('enableLearning', checked)}
                disabled={!config.enabled}
              />
            </div>

            <Separator />

            {/* Debug Mode */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Modalità Debug</Label>
                <p className="text-sm text-gray-600">
                  Logga le decisioni AI nella console
                </p>
              </div>
              <Switch
                checked={config.debugMode}
                onCheckedChange={(checked) => handleConfigChange('debugMode', checked)}
                disabled={!config.enabled}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Statistiche Dettagliate */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Statistiche Dettagliate
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Decisioni AI Addestrata</span>
                <Badge variant="default">{stats.trainedAIDecisions}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Fallback Tradizionale</span>
                <Badge variant="secondary">{stats.traditionalFallbacks}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Confidenza Media</span>
                <Badge variant="outline">{(stats.averageConfidence * 100).toFixed(1)}%</Badge>
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Distribuzione Decisioni</div>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>AI Addestrata</span>
                  <span>{getUsageRate().toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getUsageRate()}%` }}
                  />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              {stats.lastUsed && (
                <div className="text-sm">
                  <span className="font-medium">Ultimo Utilizzo:</span>
                  <br />
                  <span className="text-gray-600">
                    {new Date(stats.lastUsed).toLocaleString()}
                  </span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Avvisi */}
      {!config.enabled && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            L'AI addestrata è disabilitata. Tutte le decisioni CPU useranno la logica tradizionale.
          </AlertDescription>
        </Alert>
      )}

      {config.enabled && stats.totalDecisions === 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            L'AI addestrata è abilitata ma non ha ancora preso decisioni. 
            Inizia una partita per vedere le statistiche.
          </AlertDescription>
        </Alert>
      )}

      {config.enabled && !config.fallbackToTraditional && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Attenzione: Il fallback all'AI tradizionale è disabilitato. 
            Se l'AI addestrata fallisce, potrebbero verificarsi errori di gioco.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
