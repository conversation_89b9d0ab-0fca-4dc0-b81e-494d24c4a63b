/**
 * 🧠 COMMUNITY AI - Utilità per configurazione Storage Supabase
 * Funzioni per verificare e configurare il bucket per i dati AI
 */

import { supabase } from "@/integrations/supabase/client";

/**
 * Verifica se il bucket ai-training-data esiste
 */
export const checkAITrainingBucket = async (): Promise<{
  exists: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.storage.getBucket('ai-training-data');
    
    if (error) {
      if (error.message.includes('not found')) {
        return { exists: false };
      }
      return { exists: false, error: error.message };
    }
    
    return { exists: true };
  } catch (error) {
    return { 
      exists: false, 
      error: error instanceof Error ? error.message : 'Errore sconosciuto' 
    };
  }
};

/**
 * Crea il bucket ai-training-data se non esiste
 */
export const createAITrainingBucket = async (): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.storage.createBucket('ai-training-data', {
      public: false,
      fileSizeLimit: 102400, // 100KB
      allowedMimeTypes: ['application/json', 'application/gzip', 'text/plain']
    });
    
    if (error) {
      return { success: false, error: error.message };
    }
    
    console.log('✅ Bucket ai-training-data creato con successo');
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Errore sconosciuto' 
    };
  }
};

/**
 * Testa l'upload di un file di prova
 */
export const testAITrainingUpload = async (): Promise<{
  success: boolean;
  error?: string;
  fileSize?: number;
}> => {
  try {
    // Crea un file di test
    const testData = {
      version: "1.0",
      timestamp: Date.now(),
      test: true,
      message: "Test upload per Community AI"
    };
    
    const jsonString = JSON.stringify(testData);
    const compressedData = btoa(jsonString); // Simulazione compressione
    const fileName = `test_${Date.now()}.json.gz`;
    
    const { data, error } = await supabase.storage
      .from('ai-training-data')
      .upload(fileName, compressedData, {
        contentType: 'application/json',
        metadata: {
          version: '1.0',
          test: 'true'
        }
      });
    
    if (error) {
      return { success: false, error: error.message };
    }
    
    // Cleanup: rimuovi il file di test
    await supabase.storage
      .from('ai-training-data')
      .remove([fileName]);
    
    console.log('✅ Test upload completato con successo');
    return { 
      success: true, 
      fileSize: new Blob([compressedData]).size 
    };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Errore sconosciuto' 
    };
  }
};

/**
 * Verifica le policy del bucket
 */
export const checkBucketPolicies = async (): Promise<{
  canUpload: boolean;
  canRead: boolean;
  error?: string;
}> => {
  try {
    // Test upload (dovrebbe funzionare)
    const uploadTest = await testAITrainingUpload();
    
    // Test lettura (potrebbe non funzionare se non autenticati come admin)
    const { data: files, error: listError } = await supabase.storage
      .from('ai-training-data')
      .list();
    
    return {
      canUpload: uploadTest.success,
      canRead: !listError,
      error: uploadTest.error || listError?.message
    };
  } catch (error) {
    return {
      canUpload: false,
      canRead: false,
      error: error instanceof Error ? error.message : 'Errore sconosciuto'
    };
  }
};

/**
 * Funzione completa per setup e test del sistema Community AI
 */
export const setupCommunityAIStorage = async (): Promise<{
  success: boolean;
  details: {
    bucketExists: boolean;
    bucketCreated?: boolean;
    uploadTest: boolean;
    policiesOk: boolean;
  };
  errors: string[];
}> => {
  const errors: string[] = [];
  const details = {
    bucketExists: false,
    uploadTest: false,
    policiesOk: false
  };
  
  try {
    console.log('🧠 CommunityAI: Iniziando setup storage...');
    
    // 1. Verifica se il bucket esiste
    const bucketCheck = await checkAITrainingBucket();
    details.bucketExists = bucketCheck.exists;
    
    if (!bucketCheck.exists) {
      console.log('🧠 CommunityAI: Bucket non trovato, tentativo di creazione...');
      
      // 2. Crea il bucket se non esiste
      const createResult = await createAITrainingBucket();
      details.bucketCreated = createResult.success;
      
      if (!createResult.success) {
        errors.push(`Errore creazione bucket: ${createResult.error}`);
      }
    }
    
    // 3. Test upload
    console.log('🧠 CommunityAI: Test upload...');
    const uploadTest = await testAITrainingUpload();
    details.uploadTest = uploadTest.success;
    
    if (!uploadTest.success) {
      errors.push(`Errore test upload: ${uploadTest.error}`);
    }
    
    // 4. Verifica policy
    console.log('🧠 CommunityAI: Verifica policy...');
    const policyCheck = await checkBucketPolicies();
    details.policiesOk = policyCheck.canUpload;
    
    if (!policyCheck.canUpload) {
      errors.push(`Errore policy: ${policyCheck.error}`);
    }
    
    const success = details.uploadTest && (details.bucketExists || details.bucketCreated === true);
    
    if (success) {
      console.log('✅ CommunityAI: Setup storage completato con successo');
    } else {
      console.warn('⚠️ CommunityAI: Setup storage completato con errori:', errors);
    }
    
    return { success, details, errors };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Errore sconosciuto';
    errors.push(`Errore generale: ${errorMessage}`);
    
    return { success: false, details, errors };
  }
};

/**
 * Funzione di debug per ottenere informazioni sul bucket
 */
export const debugBucketInfo = async (): Promise<void> => {
  try {
    console.log('🔍 CommunityAI Debug: Informazioni bucket...');
    
    const bucketCheck = await checkAITrainingBucket();
    console.log('Bucket exists:', bucketCheck);
    
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    console.log('All buckets:', buckets, bucketsError);
    
    const policyCheck = await checkBucketPolicies();
    console.log('Policy check:', policyCheck);
    
  } catch (error) {
    console.error('🔍 CommunityAI Debug error:', error);
  }
};
