import { GameState } from "../../types/game";
import { Howl } from "howler";
import * as Tone from "tone";
import {
  getRandomSoundFile,
  getSoundVariation,
  KENNEY_SOUND_MAPPINGS,
  MUSIC_MAPPINGS,
} from "./SoundMappings";

export type SoundType =
  | "cardPlay"
  | "cardShuffle"
  | "cardDeal"
  | "cardFlip"
  | "cardSnap"
  | "cardGather"
  | "cardFan"
  | "cardShove"
  | "cardPackTakeOut"
  | "cardShuffleHandEnd"
  | "buttonClick"
  | "menuOpen"
  | "menuClose"
  | "turnStart"
  | "gameStart"
  | "gameEnd"
  | "roundEnd"
  | "victory"
  | "victory-cheer"
  | "defeat"
  | "trump-select"
  | "error"
  | "success"
  | "warning"
  | "notification"
  | "maraffa"
  | "gameOver";

export class AudioManager {
  private static instance: AudioManager;
  private audioContext: AudioContext | null = null;
  private isEnabled: boolean = true;
  private soundEffectsEnabled: boolean = true;
  private musicEnabled: boolean = false; // ✅ Musica disabilitata di default
  private masterVolume: number = 0.7;
  private soundEffectsVolume: number = 0.8;
  private musicVolume: number = 0.2; // ✅ Volume musica impostato al 20%
  private currentMusic: Howl | null = null;
  private soundCache: Map<string, Howl> = new Map();
  private toneInitialized: boolean = false;
  private activeSynth: Tone.PolySynth | null = null;
  private activePattern: Tone.Pattern<string[]> | null = null; // Pattern Tone.js attivo
  private wasMusicPlaying: boolean = false; // Track if music was playing before pause
  private currentTrackName: string | null = null; // Track current track name
  private isAppInBackground: boolean = false; // Track if app is in background
  private backgroundTimer: NodeJS.Timeout | null = null; // Timer for background handling
  private musicSeekPosition: number = 0; // Track music position for resume
  private constructor() {
    this.initializeAudioContext();
    this.loadSoundSettings();
    this.initializeTone();
  }

  public static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager();
    }
    return AudioManager.instance;
  }

  private async initializeTone() {
    try {
      await Tone.start();
      this.toneInitialized = true;
    } catch (error) {
      console.warn("Failed to initialize Tone.js:", error);
    }
  }
  private initializeAudioContext() {
    try {
      const AudioContext =
        window.AudioContext ||
        (
          window as typeof window & {
            webkitAudioContext: typeof window.AudioContext;
          }
        ).webkitAudioContext;
      this.audioContext = new AudioContext();
    } catch (error) {
      console.warn("Audio context not supported:", error);
    }
  }

  /**
   * 🔄 Resume AudioContext (chiamato da AudioContextManager)
   */
  public async resumeAudioContext(): Promise<void> {
    if (this.audioContext && this.audioContext.state === "suspended") {
      try {
        await this.audioContext.resume();
      } catch (error) {
        console.warn("⚠️ Failed to resume AudioManager context:", error);
      }
    }
  }
  private loadSoundSettings() {
    try {
      const settings = localStorage.getItem("audioSettings");
      if (settings) {
        const parsed = JSON.parse(settings);
        this.isEnabled = parsed.isEnabled ?? true;
        this.soundEffectsEnabled = parsed.soundEffectsEnabled ?? true;
        this.musicEnabled = parsed.musicEnabled ?? false; // ✅ Default disabilitato
        this.masterVolume = parsed.masterVolume ?? 0.7;
        this.soundEffectsVolume = parsed.soundEffectsVolume ?? 0.8;
        this.musicVolume = parsed.musicVolume ?? 0.2; // ✅ Volume al 20%
      }
    } catch (error) {
      console.warn("Error loading audio settings:", error);
    }
  }
  private saveSoundSettings() {
    try {
      const settings = {
        isEnabled: this.isEnabled,
        soundEffectsEnabled: this.soundEffectsEnabled,
        musicEnabled: this.musicEnabled,
        masterVolume: this.masterVolume,
        soundEffectsVolume: this.soundEffectsVolume,
        musicVolume: this.musicVolume,
      };
      localStorage.setItem("audioSettings", JSON.stringify(settings));
    } catch (error) {
      console.warn("Error saving audio settings:", error);
    }
  }
  public setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
    if (!enabled && this.currentMusic) {
      this.currentMusic.pause();
    }
    this.saveSoundSettings();
  }

  public setSoundEffectsEnabled(enabled: boolean) {
    this.soundEffectsEnabled = enabled;
    this.saveSoundSettings();
  }
  public setMusicEnabled(enabled: boolean) {
    this.musicEnabled = enabled;
    if (!enabled) {
      // Stop all music when disabled
      this.stopMusic();
    }
    this.saveSoundSettings();
  }

  public isAudioEnabled(): boolean {
    return this.isEnabled;
  }

  public isSoundEffectsEnabled(): boolean {
    return this.isEnabled && this.soundEffectsEnabled;
  }

  public isMusicEnabled(): boolean {
    return this.isEnabled && this.musicEnabled;
  }

  public setMasterVolume(volume: number) {
    this.masterVolume = Math.max(0, Math.min(1, volume));
    this.updateAllVolumes();
    this.saveSoundSettings();
  }

  public setSoundEffectsVolume(volume: number) {
    this.soundEffectsVolume = Math.max(0, Math.min(1, volume));
    this.saveSoundSettings();
  }
  public setMusicVolume(volume: number) {
    this.musicVolume = Math.max(0, Math.min(1, volume));
    if (this.currentMusic) {
      this.currentMusic.volume(this.masterVolume * this.musicVolume);
    }
    this.saveSoundSettings();
  }
  private updateAllVolumes() {
    // Update current music volume
    if (this.currentMusic) {
      this.currentMusic.volume(this.masterVolume * this.musicVolume);
    }

    // Update Tone.js synth volume if active - ✅ Volume ridotto
    if (this.activeSynth) {
      this.activeSynth.volume.value = Tone.gainToDb(
        this.masterVolume * this.musicVolume * 0.15 // Ridotto da 0.3 a 0.15
      );
    }

    // Update cached Kenney sounds volume
    this.soundCache.forEach((audio) => {
      audio.volume(this.masterVolume * this.soundEffectsVolume);
    });
  }

  public getMasterVolume(): number {
    return this.masterVolume;
  }

  public getSoundEffectsVolume(): number {
    return this.soundEffectsVolume;
  }

  public getMusicVolume(): number {
    return this.musicVolume;
  }
  public async playSound(
    type: SoundType,
    options: { volume?: number; pitch?: number; playerId?: number } = {}
  ) {
    if (!this.isSoundEffectsEnabled()) {
      return;
    }

    try {
      // Usa principalmente i file audio Kenney per un suono realistico
      this.playKenneyCasinoSound(type, options);
    } catch (error) {
      console.error("Error playing sound:", error);
      // Fallback alla generazione audio diretta solo se i file Kenney falliscono
      this.generateDirectAudio(type, options);
    }
  }

  /**
   * Ottiene il file audio appropriato per un tipo di suono e giocatore specifico
   * Per cardPlay, assegna un file card-place specifico per ogni giocatore (0-3)
   */
  private getSoundFileForPlayer(type: SoundType, playerId?: number): string {
    if (type === "cardPlay" && playerId !== undefined) {
      // Assegna un file card-place specifico per ogni giocatore (0-3)
      const cardPlaceFiles = KENNEY_SOUND_MAPPINGS.cardPlay;
      const playerIndex = playerId % cardPlaceFiles.length;
      return cardPlaceFiles[playerIndex];
    }

    // Per tutti gli altri suoni, usa la selezione casuale normale
    return getRandomSoundFile(type);
  }

  private playKenneyCasinoSound(
    type: SoundType,
    options: { volume?: number; pitch?: number; playerId?: number } = {}
  ) {
    // Get sound file - use specific card-place for each player
    const soundFile = this.getSoundFileForPlayer(type, options.playerId);
    const variations = getSoundVariation(type);

    // Calculate final volume
    const baseVolume = options.volume ?? variations.volume ?? 0.8;
    const finalVolume =
      baseVolume * this.masterVolume * this.soundEffectsVolume;

    // Calculate pitch (rate in Howler.js)
    const basePitch = options.pitch ?? variations.pitch ?? 1.0;

    // Check if this sound is already cached
    let sound = this.soundCache.get(soundFile);

    if (!sound) {
      // Create new Howl instance
      sound = new Howl({
        src: [soundFile],
        volume: finalVolume,
        rate: basePitch,
        preload: true,
        onloaderror: (id, error) => {
          console.error(`Failed to load sound file: ${soundFile}`, error);
          // Fallback to synthetic audio
          if (this.audioContext) {
            this.generateDirectAudio(type, options);
          }
        },
      });

      // Cache the sound for future use
      this.soundCache.set(soundFile, sound);
    } else {
      // Update volume and rate for existing sound
      sound.volume(finalVolume);
      sound.rate(basePitch);
    }

    // Play the sound
    sound.play();

    const playerInfo =
      options.playerId !== undefined ? ` (Player ${options.playerId})` : "";
  }
  private generateDirectAudio(
    type: SoundType,
    options: { volume?: number; pitch?: number } = {}
  ) {
    if (!this.audioContext) return;

    try {
      const volume =
        (options.volume ?? 1) * this.masterVolume * this.soundEffectsVolume; // Use Web Audio API for realistic card sounds
      if (this.isCardSound(type)) {
        this.generateRealisticCardSound(type, volume, options.pitch);
        return;
      }

      // Fallback to Web Audio API for other sounds
      const audioContext = this.audioContext;
      const now = audioContext.currentTime;

      // Create gain node for volume control
      const gainNode = audioContext.createGain();
      gainNode.gain.setValueAtTime(volume, now);

      // Sound parameters based on type
      let frequency = 440;
      let duration = 0.2;
      let soundType = "sine";

      switch (type) {
        case "buttonClick":
          frequency = 800;
          duration = 0.1;
          break;
        case "menuOpen":
          frequency = 523.25;
          duration = 0.2;
          soundType = "sweep";
          break;
        case "menuClose":
          frequency = 392;
          duration = 0.2;
          soundType = "sweep";
          break;
        case "turnStart":
          frequency = 698.46; // F5
          duration = 0.3;
          soundType = "bell";
          break;
        case "gameStart":
          frequency = 523.25;
          duration = 0.5;
          soundType = "fanfare";
          break;
        case "gameEnd":
          frequency = 392;
          duration = 0.8;
          soundType = "chord";
          break;
        case "victory":
          frequency = 659.25;
          duration = 1.0;
          soundType = "chord";
          break;
        case "defeat":
          frequency = 196;
          duration = 0.8;
          soundType = "chord";
          break;
        case "error":
          frequency = 200;
          duration = 0.3;
          soundType = "noise";
          break;
        case "success":
          frequency = 880;
          duration = 0.4;
          soundType = "ding";
          break;
        case "warning":
          frequency = 440;
          duration = 0.2;
          soundType = "bell";
          break;
        case "notification":
          frequency = 1046.5; // C6
          duration = 0.2;
          soundType = "bell";
          break;
        case "roundEnd":
          frequency = 523.25; // C5
          duration = 0.6;
          soundType = "chord";
          break;
        case "victory-cheer":
          frequency = 783.99; // G5
          duration = 1.2;
          soundType = "fanfare";
          break;
        case "trump-select":
          frequency = 659.25; // E5
          duration = 0.3;
          soundType = "ding";
          break;
        case "cardGather":
          frequency = 440;
          duration = 0.4;
          soundType = "sweep";
          break;
      }

      // Apply pitch modification if provided
      if (options.pitch) {
        frequency *= options.pitch;
      }

      // Generate appropriate sound based on type
      if (soundType === "chord") {
        // Accordo per vittoria
        const frequencies = [frequency, frequency * 1.25, frequency * 1.5];
        frequencies.forEach((freq) => {
          const oscillator = audioContext.createOscillator();
          oscillator.frequency.setValueAtTime(freq, now);
          oscillator.type = "sine";
          oscillator.connect(gainNode);
          oscillator.start(now);
          oscillator.stop(now + duration);
        });
        gainNode.connect(audioContext.destination);
      } else if (soundType === "sweep") {
        // Sweep per menu
        const oscillator = audioContext.createOscillator();
        oscillator.frequency.setValueAtTime(frequency, now);
        oscillator.frequency.exponentialRampToValueAtTime(
          frequency * 2,
          now + duration
        );
        oscillator.type = "sine";
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.start(now);
        oscillator.stop(now + duration);
      } else if (soundType === "bell") {
        // Campana per notifiche
        const oscillator = audioContext.createOscillator();
        oscillator.frequency.setValueAtTime(frequency, now);
        oscillator.frequency.exponentialRampToValueAtTime(
          frequency * 0.5,
          now + duration
        );
        oscillator.type = "sine";
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.start(now);
        oscillator.stop(now + duration);
      } else if (soundType === "ding") {
        // Ding per punteggio
        const oscillator = audioContext.createOscillator();
        oscillator.frequency.setValueAtTime(frequency, now);
        oscillator.type = "triangle";
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.start(now);
        oscillator.stop(now + duration);
      } else if (soundType === "fanfare") {
        // Fanfara per inizio gioco
        const notes = [
          frequency,
          frequency * 1.125,
          frequency * 1.25,
          frequency * 1.5,
        ];
        notes.forEach((freq, index) => {
          const oscillator = audioContext.createOscillator();
          const startTime = now + index * 0.1;
          oscillator.frequency.setValueAtTime(freq, startTime);
          oscillator.type = "sawtooth";

          const noteGain = audioContext.createGain();
          noteGain.gain.setValueAtTime(0, startTime);
          noteGain.gain.linearRampToValueAtTime(volume * 0.3, startTime + 0.05);
          noteGain.gain.exponentialRampToValueAtTime(0.001, startTime + 0.15);

          oscillator.connect(noteGain);
          noteGain.connect(audioContext.destination);
          oscillator.start(startTime);
          oscillator.stop(startTime + 0.15);
        });
      } else if (soundType === "noise") {
        // Generate noise for error effects
        const bufferSize = audioContext.sampleRate * duration;
        const buffer = audioContext.createBuffer(
          1,
          bufferSize,
          audioContext.sampleRate
        );
        const data = buffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
          const time = i / audioContext.sampleRate;
          const noise = (Math.random() * 2 - 1) * 0.3;
          const envelope = Math.sin((time * Math.PI) / duration);
          data[i] = noise * envelope;
        }

        const source = audioContext.createBufferSource();
        source.buffer = buffer;
        source.connect(gainNode);
        gainNode.connect(audioContext.destination);
        source.start(now);
      } else {
        // Tono semplice (sine wave)
        const oscillator = audioContext.createOscillator();
        oscillator.frequency.setValueAtTime(frequency, now);
        oscillator.type = "sine";
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        oscillator.start(now);
        oscillator.stop(now + duration);
      }
    } catch (error) {
      console.error("Error playing direct audio:", error);
    }
  }

  private isCardSound(type: SoundType): boolean {
    return [
      "cardPlay",
      "cardShuffle",
      "cardDeal",
      "cardFlip",
      "cardSnap",
    ].includes(type);
  }
  private generateRealisticCardSound(
    type: SoundType,
    volume: number,
    pitch?: number
  ) {
    if (!this.audioContext) return;

    try {
      // Use more realistic physical modeling for card sounds
      switch (type) {
        case "cardShuffle":
          this.generateRealisticShuffleSound(volume);
          break;
        case "cardPlay":
        case "cardDeal":
          this.generateRealisticSlideSound(volume, pitch);
          break;
        case "cardFlip":
          this.generateRealisticFlipSound(volume, pitch);
          break;
        case "cardSnap":
          this.generateRealisticSnapSound(volume, pitch);
          break;
      }
    } catch (error) {
      console.error("Error generating realistic card sound:", error);
    }
  }
  private generateRealisticShuffleSound(volume: number) {
    if (!this.audioContext) return;

    // Simulate real card shuffling - multiple overlapping friction sounds
    const audioContext = this.audioContext;
    const now = audioContext.currentTime;
    const duration = 0.8;

    // Create multiple noise sources for realistic shuffle
    for (let i = 0; i < 8; i++) {
      const startTime = now + i * 0.1;
      const shuffleDuration = 0.3;

      // Generate filtered noise for card friction
      const bufferSize = audioContext.sampleRate * shuffleDuration;
      const buffer = audioContext.createBuffer(
        1,
        bufferSize,
        audioContext.sampleRate
      );
      const data = buffer.getChannelData(0);

      // Create realistic friction noise
      for (let j = 0; j < bufferSize; j++) {
        const time = j / audioContext.sampleRate;
        const noise = Math.random() * 2 - 1;
        const lowFreq = Math.sin(time * Math.PI * 150) * 0.3;
        const friction = noise * 0.7 + lowFreq;
        const envelope =
          Math.exp(-time * 3) * Math.sin((time * Math.PI) / shuffleDuration);
        data[j] = friction * envelope * 0.4;
      }

      const source = audioContext.createBufferSource();
      source.buffer = buffer;

      // Apply realistic filtering
      const filter = audioContext.createBiquadFilter();
      filter.type = "bandpass";
      filter.frequency.value = 400 + Math.random() * 600;
      filter.Q.value = 2;

      const gainNode = audioContext.createGain();
      gainNode.gain.setValueAtTime(0, startTime);
      gainNode.gain.linearRampToValueAtTime(volume * 0.6, startTime + 0.05);
      gainNode.gain.exponentialRampToValueAtTime(
        0.001,
        startTime + shuffleDuration - 0.05
      );

      source.connect(filter);
      filter.connect(gainNode);
      gainNode.connect(audioContext.destination);

      source.start(startTime);
      source.stop(startTime + shuffleDuration);
    }
  }

  private generateRealisticSlideSound(volume: number, pitch?: number) {
    if (!this.audioContext) return;

    // Simulate card sliding on table surface
    const audioContext = this.audioContext;
    const now = audioContext.currentTime;
    const duration = 0.25;

    // Create friction sound buffer
    const bufferSize = audioContext.sampleRate * duration;
    const buffer = audioContext.createBuffer(
      1,
      bufferSize,
      audioContext.sampleRate
    );
    const data = buffer.getChannelData(0);

    // Generate realistic sliding friction
    for (let i = 0; i < bufferSize; i++) {
      const time = i / audioContext.sampleRate;
      const progress = time / duration;

      // Base friction noise
      const noise = (Math.random() * 2 - 1) * 0.3;

      // Low frequency rumble for table contact
      const rumble = Math.sin(time * Math.PI * 80) * 0.2;

      // High frequency scratch
      const scratch =
        (Math.random() * 2 - 1) * 0.1 * Math.sin(time * Math.PI * 1200);

      // Velocity envelope (faster at start, slower at end)
      const velocity = Math.exp(-progress * 2);
      const envelope = Math.sin(progress * Math.PI) * velocity;

      data[i] = (noise + rumble + scratch) * envelope;
    }

    const source = audioContext.createBufferSource();
    source.buffer = buffer;

    // Apply filtering for more realistic sound
    const lowpass = audioContext.createBiquadFilter();
    lowpass.type = "lowpass";
    lowpass.frequency.value = (pitch || 1) * 1500;
    lowpass.Q.value = 1;

    const highpass = audioContext.createBiquadFilter();
    highpass.type = "highpass";
    highpass.frequency.value = 120;
    highpass.Q.value = 0.5;

    const gainNode = audioContext.createGain();
    gainNode.gain.setValueAtTime(volume * 0.8, now);

    source.connect(highpass);
    highpass.connect(lowpass);
    lowpass.connect(gainNode);
    gainNode.connect(audioContext.destination);

    source.start(now);

    // Add subtle "thud" at the end
    setTimeout(() => {
      this.generateCardThud(volume * 0.3);
    }, duration * 800);
  }

  private generateRealisticFlipSound(volume: number, pitch?: number) {
    if (!this.audioContext) return;

    // Simulate card flipping through air
    const audioContext = this.audioContext;
    const now = audioContext.currentTime;
    const duration = 0.12;

    // Quick whoosh with realistic air displacement
    const bufferSize = audioContext.sampleRate * duration;
    const buffer = audioContext.createBuffer(
      1,
      bufferSize,
      audioContext.sampleRate
    );
    const data = buffer.getChannelData(0);

    for (let i = 0; i < bufferSize; i++) {
      const time = i / audioContext.sampleRate;
      const progress = time / duration;

      // Air movement noise
      const airNoise = (Math.random() * 2 - 1) * 0.4;

      // Quick frequency sweep for card movement
      const sweepFreq = 800 + progress * 1200;
      const sweep = Math.sin(time * Math.PI * sweepFreq) * 0.3;

      // Sharp envelope for quick flip
      const envelope = Math.exp(-progress * 8) * Math.sin(progress * Math.PI);

      data[i] = (airNoise + sweep) * envelope;
    }

    const source = audioContext.createBufferSource();
    source.buffer = buffer;

    const filter = audioContext.createBiquadFilter();
    filter.type = "bandpass";
    filter.frequency.value = (pitch || 1) * 1200;
    filter.Q.value = 3;

    const gainNode = audioContext.createGain();
    gainNode.gain.setValueAtTime(volume * 0.6, now);

    source.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);

    source.start(now);
  }

  private generateRealisticSnapSound(volume: number, pitch?: number) {
    if (!this.audioContext) return;

    // Sharp card snap sound
    const audioContext = this.audioContext;
    const now = audioContext.currentTime;
    const duration = 0.08;

    // Create sharp click buffer
    const bufferSize = audioContext.sampleRate * duration;
    const buffer = audioContext.createBuffer(
      1,
      bufferSize,
      audioContext.sampleRate
    );
    const data = buffer.getChannelData(0);

    for (let i = 0; i < bufferSize; i++) {
      const time = i / audioContext.sampleRate;
      const progress = time / duration;

      // Sharp transient
      const click = progress < 0.1 ? Math.sin(progress * Math.PI * 50) : 0;

      // Brief resonance
      const resonance =
        Math.sin(time * Math.PI * 2400 * (pitch || 1)) *
        Math.exp(-progress * 15);

      // Noise component
      const noise = (Math.random() * 2 - 1) * 0.2 * Math.exp(-progress * 20);

      data[i] = (click + resonance + noise) * Math.exp(-progress * 12);
    }

    const source = audioContext.createBufferSource();
    source.buffer = buffer;

    const gainNode = audioContext.createGain();
    gainNode.gain.setValueAtTime(volume * 0.9, now);

    source.connect(gainNode);
    gainNode.connect(audioContext.destination);

    source.start(now);
  }

  private generateCardThud(volume: number) {
    if (!this.audioContext) return;

    // Subtle thud when card lands
    const audioContext = this.audioContext;
    const now = audioContext.currentTime;
    const duration = 0.05;

    const oscillator = audioContext.createOscillator();
    oscillator.frequency.setValueAtTime(60, now);
    oscillator.frequency.exponentialRampToValueAtTime(30, now + duration);
    oscillator.type = "sine";

    const gainNode = audioContext.createGain();
    gainNode.gain.setValueAtTime(volume, now);
    gainNode.gain.exponentialRampToValueAtTime(0.001, now + duration);

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    oscillator.start(now);
    oscillator.stop(now + duration);
  }
  public async playMusic(trackName: string, loop: boolean = true) {
    if (!this.isMusicEnabled()) {
      return;
    }

    // Prevenzione sovrapposizioni: Se la stessa traccia è già attiva, non fare nulla
    if (this.currentTrackName === trackName && this.currentMusic?.playing()) {
      return;
    }

    try {
      this.currentTrackName = trackName;
      this.wasMusicPlaying = true;

      // Ferma musica corrente se è una traccia diversa
      if (this.currentMusic && this.currentTrackName !== trackName) {
        this.currentMusic.stop();
        this.currentMusic = null;
      }

      // Ferma pattern e synth Tone.js attivi se cambio traccia
      this.stopToneAudio();

      // Ottieni file musicale
      const musicFile = MUSIC_MAPPINGS[trackName];
      if (!musicFile) {
        console.warn(
          `Nessun file musicale trovato per: ${trackName}, uso fallback generato`
        );
        this.generateBackgroundMusic(trackName, loop);
        return;
      }

      const finalVolume = this.masterVolume * this.musicVolume;

      // Create new Howl instance for the music file
      this.currentMusic = new Howl({
        src: [musicFile],
        loop: loop,
        volume: finalVolume,
        preload: true,

        onloaderror: (id, error) => {
          // Fallback to generated music
          this.generateBackgroundMusic(trackName, loop);
        },

        onend: () => {
          if (!loop) {
            this.currentMusic = null;
          }
        },
      });

      // Play the music
      this.currentMusic.play();
    } catch (error) {
      console.error("Error playing music:", error);
      // Fallback to generated music
      this.generateBackgroundMusic(trackName, loop);
    }
  }

  private generateBackgroundMusic(trackName: string, loop: boolean = true) {
    if (!this.toneInitialized) {
      console.warn("Tone.js not initialized, cannot generate background music");
      return;
    }

    // 🎵 PREVENZIONE SOVRAPPOSIZIONI: Se la stessa traccia generata è già attiva, non fare nulla
    if (
      this.currentTrackName === trackName &&
      this.activePattern &&
      this.activeSynth
    ) {
      return;
    }

    try {
      // Stop previous pattern if exists and different track
      if (this.activePattern && this.currentTrackName !== trackName) {
        this.activePattern.stop();
        this.activePattern.dispose();
        this.activePattern = null;
      }

      // Stop previous synth if exists and different track
      if (this.activeSynth && this.currentTrackName !== trackName) {
        this.activeSynth.dispose();
        this.activeSynth = null;
      }

      this.currentTrackName = trackName;

      // Crea generatore musicale ambientale semplice
      const synth = new Tone.PolySynth().toDestination();
      const reverb = new Tone.Reverb(4).toDestination();
      synth.connect(reverb);
      this.activeSynth = synth;

      // Imposta volume basso per musica generata
      synth.volume.value = Tone.gainToDb(
        this.masterVolume * this.musicVolume * 0.15
      );

      // Definisci progressioni di accordi per traccia
      const { chords, tempo } = this.getChordProgression(trackName);

      // Crea pattern che suona gli accordi
      const pattern = new Tone.Pattern((time, chord) => {
        // Check if synth is still available before playing
        if (this.activeSynth && !this.activeSynth.disposed) {
          synth.triggerAttackRelease(chord, "2n", time);
        }
      }, chords);

      // Track the active pattern
      this.activePattern = pattern;

      pattern.interval = "2n";
      Tone.Transport.bpm.value = tempo;

      if (loop) {
        pattern.iterations = Infinity;
      }

      pattern.start();
      Tone.Transport.start();

      // Store reference for cleanup
      this.currentMusic = new Howl({
        src: [
          "data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+D0wWsiBDGH0fPTgjMGHm7A7+OZUR0PNHnE8NyJOggVX7zn6qhWFQlMn97yu2kiBjWH0fHTgTICHm3A7+OZURE6",
        ], // Empty base64 audio
        loop: false,
        volume: 0,
      });
    } catch (error) {
      console.error("Error generating background music:", error);
    }
  }

  /**
   * Ottieni progressione di accordi basata sul nome della traccia
   */
  private getChordProgression(trackName: string): {
    chords: string[][];
    tempo: number;
  } {
    switch (trackName) {
      case "menu":
        return {
          chords: [
            ["C4", "E4", "G4"],
            ["F4", "A4", "C5"],
            ["G4", "B4", "D5"],
            ["C4", "E4", "G4"],
          ],
          tempo: 70,
        };
      case "game":
        return {
          chords: [
            ["A3", "C4", "E4"],
            ["F3", "A3", "C4"],
            ["G3", "B3", "D4"],
            ["A3", "C4", "E4"],
          ],
          tempo: 80,
        };
      default:
        return {
          chords: [
            ["D4", "F#4", "A4"],
            ["G3", "B3", "D4"],
            ["A3", "C4", "E4"],
            ["D4", "F#4", "A4"],
          ],
          tempo: 65,
        };
    }
  }

  public stopMusic() {
    this.wasMusicPlaying = false;
    this.currentTrackName = null;
    this.musicSeekPosition = 0; // Reset position quando si ferma manualmente

    if (this.currentMusic) {
      this.currentMusic.stop();
      this.currentMusic = null;
    }

    // Usa il nuovo helper per pulire Tone.js
    this.stopToneAudio();

    // Ferma il transport Tone.js
    if (this.toneInitialized) {
      Tone.Transport.stop();
      Tone.Transport.cancel();
    }
  }

  public pauseMusic() {
    // Track if music was playing before pause
    this.wasMusicPlaying = !!(this.currentMusic || this.activePattern);

    if (this.currentMusic) {
      this.currentMusic.pause();
    }

    if (this.toneInitialized) {
      Tone.Transport.pause();
    }
  }
  public resumeMusic() {
    // Only resume if music was playing before pause and music is enabled
    if (!this.wasMusicPlaying || !this.isMusicEnabled()) {
      return;
    }

    // ✅ PREVENZIONE DUPLICAZIONI: Se la musica è già in riproduzione, non fare nulla
    if (this.currentMusic && this.currentMusic.playing()) {
      return;
    }

    // ✅ GESTIONE MIGLIORATA: Riprende solo se in pausa, altrimenti riavvia cautamente
    if (this.currentMusic) {
      // Se la musica esiste ma non sta suonando, riprendila
      try {
        this.currentMusic.play();
      } catch (error) {
        console.warn("Error resuming music, restarting track:", error);
        // Solo in caso di errore, riavvia la traccia
        if (this.currentTrackName) {
          this.playMusic(this.currentTrackName);
        }
      }
    } else if (this.currentTrackName) {
      // Se non c'è musica corrente ma avevamo una traccia, riavviala
      this.playMusic(this.currentTrackName);
    }

    // Gestisci Tone.js transport
    if (this.toneInitialized && Tone.Transport.state !== "started") {
      try {
        Tone.Transport.start();
      } catch (error) {
        console.warn("Error starting Tone.js transport:", error);
      }
    }
  }

  /**
   * Verifica se una specifica traccia musicale è attualmente in riproduzione
   */
  public isTrackPlaying(trackName: string): boolean {
    if (this.currentTrackName !== trackName) {
      return false;
    }

    // Controlla se la musica Howl è in riproduzione
    if (this.currentMusic && this.currentMusic.playing()) {
      return true;
    }

    // Controlla se la musica generata (Tone.js) è attiva
    if (
      this.activePattern &&
      this.activeSynth &&
      Tone.Transport.state === "started"
    ) {
      return true;
    }

    return false;
  }

  /**
   * Ottiene il nome della traccia attualmente in riproduzione
   */
  public getCurrentTrackName(): string | null {
    return this.currentTrackName;
  }

  public preloadSounds(soundTypes: SoundType[]) {
    soundTypes.forEach((soundType) => {
      try {
        const soundFile = getRandomSoundFile(soundType);

        if (!this.soundCache.has(soundFile)) {
          const sound = new Howl({
            src: [soundFile],
            preload: true,
            volume: this.masterVolume * this.soundEffectsVolume,

            onloaderror: (id, error) => {
              console.warn(`❌ Failed to preload: ${soundFile}`, error);
            },
          });

          this.soundCache.set(soundFile, sound);
        }
      } catch (error) {
        console.error(`Error preloading sound ${soundType}:`, error);
      }
    });
  }
  public playGameStateSound(gameState: GameState) {
    switch (gameState.gamePhase) {
      case "setup":
        this.playSound("gameStart");
        break;
      case "play":
        this.playSound("turnStart");
        break;
      case "gameOver":
        this.playSound("gameEnd");
        break;
      default:
        break;
    }
  }

  public dispose() {
    this.stopMusic();
    this.soundCache.clear();

    // Clear background timer
    if (this.backgroundTimer) {
      clearTimeout(this.backgroundTimer);
      this.backgroundTimer = null;
    }

    if (this.audioContext && this.audioContext.state !== "closed") {
      this.audioContext.close();
    }
  }
  /**
   * Helper per fermare l'audio Tone.js attivo
   */
  private stopToneAudio() {
    if (this.activePattern) {
      this.activePattern.stop();
      this.activePattern.dispose();
      this.activePattern = null;
    }

    if (this.activeSynth) {
      this.activeSynth.dispose();
      this.activeSynth = null;
    }
  }

  /**
   * 🛑 FERMA COMPLETAMENTE TUTTI I SUONI E LA MUSICA - VERSIONE SUPER AGGRESSIVA
   * Utilizzato quando l'app va in background/viene chiusa
   */
  public async stopAllAudio() {
    console.log("🔇 STOP ALL AUDIO - VERSIONE SUPER AGGRESSIVA");

    // Ferma tutta la musica
    this.stopMusic();

    // Ferma tutti i suoni nella cache con metodi multipli
    this.soundCache.forEach((sound) => {
      try {
        // Metodi multipli per essere sicuri
        if (sound.playing()) {
          sound.stop();
        }
        sound.pause();
        sound.volume(0); // Forza volume a 0
        sound.mute(true); // Muta il suono

        // Prova anche a fermare tutti gli ID attivi
        const soundIds = sound._sounds || [];
        soundIds.forEach((soundData: any) => {
          if (soundData && soundData._id) {
            try {
              sound.stop(soundData._id);
              sound.pause(soundData._id);
            } catch (e) {
              // Ignora errori
            }
          }
        });
      } catch (error) {
        // Ignora errori di stop/pause
      }
    });

    // Ferma completamente Tone.js con metodi multipli
    if (this.toneInitialized) {
      try {
        Tone.Transport.stop();
        Tone.Transport.cancel();
        this.stopToneAudio();

        // Prova a sospendere il context audio
        if (Tone.context.state === "running") {
          try {
            await Tone.context.suspend();
          } catch (suspendError) {
            // Ignora errori di sospensione
          }
        }

        // Prova anche a chiudere il context se possibile
        if (
          Tone.context.state !== "closed" &&
          typeof Tone.context.close === "function"
        ) {
          try {
            // Non chiudere il context perché potrebbe causare problemi al resume
            // await Tone.context.close();
          } catch (closeError) {
            // Ignora errori di chiusura
          }
        }
      } catch (error) {
        // Ignora errori di stop Tone.js
      }
    }

    // Ferma anche il context audio nativo se disponibile
    if (this.audioContext && this.audioContext.state === "running") {
      try {
        await this.audioContext.suspend();
      } catch (error) {
        // Ignora errori
      }
    }

    // Reset dei flag - NON resettare wasMusicPlaying qui per permettere il resume
    this.currentTrackName = null;

    console.log("✅ STOP ALL AUDIO COMPLETATO");
  }

  /**
   * 📱 Gestisce quando l'app va in background (Android/iOS)
   * Ferma immediatamente tutti i suoni e marca lo stato - VERSIONE MIGLIORATA
   */
  public async onAppBackground(): Promise<void> {
    console.log("🔇 onAppBackground chiamato");

    // 🌐 SU WEB NON FARE NULLA - LASCIA CHE L'UTENTE CONTROLLI
    if (!this.isMobileApp()) {
      console.log("🌐 Web: ignoro background, utente controlla audio");
      return;
    }

    // Clear any pending timer
    if (this.backgroundTimer) {
      clearTimeout(this.backgroundTimer);
      this.backgroundTimer = null;
    }

    this.isAppInBackground = true;

    // Save current music state and position before stopping
    const wasPlaying = this.isTrackPlaying(this.currentTrackName || "");
    this.wasMusicPlaying = wasPlaying;

    console.log(
      `🔇 Stato musica prima del background: playing=${wasPlaying}, track=${this.currentTrackName}`
    );

    // Salva la posizione corrente della musica per riprendere dal punto esatto
    if (this.wasMusicPlaying && this.currentMusic) {
      try {
        // Usa seek() senza parametri per ottenere la posizione corrente
        const position = this.currentMusic.seek();
        this.musicSeekPosition = typeof position === "number" ? position : 0;
        console.log(`🔇 Posizione musica salvata: ${this.musicSeekPosition}`);
      } catch (error) {
        console.error(
          "❌ Errore nel salvare la posizione della musica:",
          error
        );
        this.musicSeekPosition = 0;
      }
    } else {
      // Reset position se non c'è musica in riproduzione
      this.musicSeekPosition = 0;
    }

    // Stop all audio immediately
    await this.stopAllAudio();
    console.log("✅ Audio fermato completamente");
  }

  /**
   * 📱 Gestisce quando l'app torna in foreground (Android/iOS)
   * Riprende l'audio con debouncing ridotto - VERSIONE MIGLIORATA
   */
  public onAppForeground(): void {
    console.log("🔊 onAppForeground chiamato");

    // 🌐 SU WEB NON FARE NULLA - LASCIA CHE L'UTENTE CONTROLLI
    if (!this.isMobileApp()) {
      console.log("🌐 Web: ignoro foreground, utente controlla audio");
      return;
    }

    this.isAppInBackground = false;

    // Clear any pending timer
    if (this.backgroundTimer) {
      clearTimeout(this.backgroundTimer);
    }

    // Use minimal debouncing to avoid multiple rapid calls
    this.backgroundTimer = setTimeout(() => {
      console.log(
        `🔊 Controllo resume: wasMusicPlaying=${
          this.wasMusicPlaying
        }, currentTrackName=${
          this.currentTrackName
        }, musicEnabled=${this.isMusicEnabled()}, seekPosition=${
          this.musicSeekPosition
        }`
      );

      if (
        !this.isAppInBackground &&
        this.wasMusicPlaying &&
        this.currentTrackName
      ) {
        console.log(
          `🔊 Riprendendo traccia: ${this.currentTrackName} dalla posizione ${this.musicSeekPosition}`
        );

        // 📱 MOBILE: RIPRENDI SEMPRE SE ERA IN RIPRODUZIONE
        if (this.isMusicEnabled()) {
          console.log("🔊 MOBILE: Riprendo musica immediatamente");

          // Riavvia la musica
          this.playMusic(this.currentTrackName);

          // 📱 MOBILE: Seek migliorato con singolo tentativo ritardato
          const seekToPosition = () => {
            if (this.currentMusic && this.musicSeekPosition > 0) {
              try {
                // Verifica che la musica sia effettivamente caricata
                const duration = this.currentMusic.duration();
                if (duration > 0 && this.musicSeekPosition < duration) {
                  this.currentMusic.seek(this.musicSeekPosition);
                  console.log(
                    `✅ MOBILE: Musica ripresa da ${this.musicSeekPosition}s di ${duration}s totali`
                  );
                  // Reset della posizione dopo successo
                  this.musicSeekPosition = 0;
                } else {
                  console.warn(
                    `⚠️ MOBILE: Posizione ${this.musicSeekPosition} non valida per durata ${duration}, riprovo tra 500ms`
                  );
                  // Riprova una sola volta dopo 500ms se la durata non è ancora disponibile
                  setTimeout(() => {
                    if (this.currentMusic && this.musicSeekPosition > 0) {
                      const newDuration = this.currentMusic.duration();
                      if (
                        newDuration > 0 &&
                        this.musicSeekPosition < newDuration
                      ) {
                        this.currentMusic.seek(this.musicSeekPosition);
                        console.log(
                          `✅ MOBILE: Musica ripresa (retry) da ${this.musicSeekPosition}s`
                        );
                      }
                      this.musicSeekPosition = 0;
                    }
                  }, 500);
                }
              } catch (error) {
                console.error(
                  "❌ MOBILE: Errore nel seek della musica:",
                  error
                );
                this.musicSeekPosition = 0;
              }
            }
          };

          // 📱 MOBILE: Singolo tentativo con delay appropriato
          setTimeout(seekToPosition, 250);
        } else {
          console.log(
            "🔇 MOBILE: Musica era in riproduzione ma ora è disabilitata nelle impostazioni"
          );
          // Reset state since music is disabled
          this.wasMusicPlaying = false;
          this.musicSeekPosition = 0;
        }
      } else {
        console.log("🔇 Non riprendo la musica: condizioni non soddisfatte");
        console.log(`  - isAppInBackground: ${this.isAppInBackground}`);
        console.log(`  - wasMusicPlaying: ${this.wasMusicPlaying}`);
        console.log(`  - currentTrackName: ${this.currentTrackName}`);
      }
      this.backgroundTimer = null;
    }, 500); // Aumentato il debouncing per dare più tempo al sistema mobile
  }

  /**
   * 🔍 Verifica se l'app è attualmente in background
   */
  public getIsAppInBackground(): boolean {
    return this.isAppInBackground;
  }

  /**
   * 📱 Verifica se siamo su Android
   */
  private isAndroid(): boolean {
    try {
      const { Capacitor } = require("@capacitor/core");
      return (
        Capacitor.isNativePlatform() && Capacitor.getPlatform() === "android"
      );
    } catch {
      return false;
    }
  }

  /**
   * 📱 Verifica se siamo su iOS
   */
  private isIOS(): boolean {
    try {
      const { Capacitor } = require("@capacitor/core");
      return Capacitor.isNativePlatform() && Capacitor.getPlatform() === "ios";
    } catch {
      return false;
    }
  }

  /**
   * 📱 Verifica se siamo su un'app mobile (Android o iOS)
   */
  private isMobileApp(): boolean {
    return this.isAndroid() || this.isIOS();
  }
}

// Export singleton instance
export const audioManager = AudioManager.getInstance();

// 🌐 Esponi AudioManager globalmente per permettere l'accesso da Android
declare global {
  interface Window {
    AudioManager: typeof AudioManager;
  }
}

// Esponi AudioManager a livello globale
if (typeof window !== "undefined") {
  window.AudioManager = AudioManager;
}
