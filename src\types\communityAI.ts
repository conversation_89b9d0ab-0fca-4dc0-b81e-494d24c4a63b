/**
 * 🧠 COMMUNITY AI - Interfacce TypeScript
 * Definizioni per il sistema di raccolta dati delle partite
 */

import { Card, Suit } from "@/utils/game/cardUtils";

/**
 * Rappresenta una singola mossa durante la partita
 */
export interface GameMove {
  /** Numero del turno (1-40 per una partita completa) */
  turn: number;
  /** Indice del giocatore che ha giocato la carta (0-3) */
  playerIndex: number;
  /** Carta giocata */
  playedCard: Card;
  /** <PERSON><PERSON> rimanenti in mano del giocatore prima della mossa */
  handBefore: Card[];
  /** Carte già giocate nel trick corrente prima di questa mossa */
  trickCardsBefore: Card[];
  /** Seme di briscola attivo */
  trumpSuit: Suit | null;
  /** Seme di uscita del trick */
  leadSuit: Suit | null;
  /** Indice del vincitore del trick (disponibile solo dopo l'ultima carta del trick) */
  trickWinner?: number;
  /** Punti ottenuti nel trick (disponibile solo dopo l'ultima carta del trick) */
  trickPoints?: number;
  /** Indica se il giocatore è AI */
  isAI: boolean;
  /** Difficoltà AI se applicabile */
  aiDifficulty?: "easy" | "medium" | "hard";
}

/**
 * Rappresenta lo stato di un team
 */
export interface TeamData {
  /** Indici dei giocatori nel team */
  playerIndices: [number, number];
  /** Nomi dei giocatori */
  playerNames: [string, string];
  /** Indica quali giocatori sono AI */
  isAI: [boolean, boolean];
  /** Difficoltà AI per ogni giocatore */
  aiDifficulties: [("easy" | "medium" | "hard" | null), ("easy" | "medium" | "hard" | null)];
}

/**
 * Dati completi di una partita per il training AI
 */
export interface GameTrainingData {
  /** Versione del formato dati */
  version: string;
  /** Timestamp di inizio partita */
  timestamp: number;
  /** Durata della partita in millisecondi */
  duration: number;
  /** Configurazione dei team */
  teams: {
    team0: TeamData;
    team1: TeamData;
  };
  /** Tutte le mosse della partita in ordine cronologico */
  moves: GameMove[];
  /** Punteggio finale */
  finalScore: {
    team0: number;
    team1: number;
  };
  /** Team vincitore (0 o 1) */
  winnerTeam: 0 | 1;
  /** Punti vittoria configurati (21, 31, 41) */
  victoryPoints: number;
  /** Numero di maraffe realizzate per team */
  maraffeMade: [number, number];
  /** Indica se è stata una vittoria dominante (>10 punti di differenza) */
  isDominantWin: boolean;
  /** Metadati aggiuntivi */
  metadata: {
    /** Indica se il giocatore umano ha vinto */
    humanPlayerWon: boolean;
    /** Difficoltà media degli AI */
    averageAIDifficulty: "easy" | "medium" | "hard";
    /** Numero totale di trick giocati */
    totalTricks: number;
  };
}

/**
 * Dati compressi per l'invio a Supabase
 */
export interface CompressedGameData {
  /** ID univoco del file */
  id: string;
  /** Timestamp di creazione */
  timestamp: number;
  /** Dati compressi in base64 */
  compressedData: string;
  /** Dimensione originale in bytes */
  originalSize: number;
  /** Dimensione compressa in bytes */
  compressedSize: number;
  /** Versione del formato */
  version: string;
}

/**
 * Configurazione per il servizio Community AI
 */
export interface CommunityAIConfig {
  /** Abilita/disabilita la raccolta dati */
  enabled: boolean;
  /** Dimensione massima del file compresso (in KB) */
  maxCompressedSize: number;
  /** Numero massimo di tentativi di invio */
  maxRetries: number;
  /** Timeout per l'invio in millisecondi */
  uploadTimeout: number;
  /** Bucket Supabase per i dati */
  supabaseBucket: string;
}

/**
 * Risultato dell'operazione di invio dati
 */
export interface UploadResult {
  /** Indica se l'invio è riuscito */
  success: boolean;
  /** ID del file caricato (se successo) */
  fileId?: string;
  /** Messaggio di errore (se fallimento) */
  error?: string;
  /** Dimensione del file caricato */
  fileSize?: number;
  /** Tempo impiegato per l'upload in millisecondi */
  uploadTime?: number;
}

/**
 * Statistiche del sistema Community AI
 */
export interface CommunityAIStats {
  /** Numero totale di partite raccolte */
  totalGamesCollected: number;
  /** Numero di partite inviate con successo */
  successfulUploads: number;
  /** Numero di errori di invio */
  uploadErrors: number;
  /** Dimensione totale dei dati inviati (in KB) */
  totalDataUploaded: number;
  /** Data dell'ultimo invio riuscito */
  lastSuccessfulUpload?: string;
  /** Data dell'ultimo errore */
  lastError?: string;
}
