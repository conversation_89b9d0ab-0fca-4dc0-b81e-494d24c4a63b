# Integrazione AI Apprendimento per Maraffa Romagnola

## Panoramica

Questo documento descrive l'approccio migliore per integrare un sistema di intelligenza artificiale che apprende dalle partite giocate per migliorare le decisioni della CPU nel gioco Maraffa Romagnola.

## Obiettivi

- **Apprendimento Offline**: Sistema completamente offline, senza dipendenze esterne
- **Peso Ridotto**: Impatto minimo sulle dimensioni dell'app
- **Performance**: Nessun rallentamento durante il gameplay
- **Miglioramento Graduale**: La CPU diventa progressivamente più intelligente

## Approccio Raccomandato: Reinforcement Learning Leggero

### 1. Architettura Proposta

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Game Engine   │───▶│  Experience      │───▶│   AI Learning   │
│                 │    │  Collector       │    │   Module        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  Game History    │    │   Policy        │
                       │  Database        │    │   Network       │
                       └──────────────────┘    └─────────────────┘
```

### 2. Componenti Principali

#### A. Experience Collector
- **Funzione**: Raccoglie dati durante ogni partita
- **Dati Raccolti**:
  - Stato del gioco (carte in mano, carte giocate, briscola)
  - Azione presa dalla CPU
  - Risultato dell'azione (punti ottenuti, vittoria/sconfitta del trick)
  - Risultato finale della partita

#### B. Game History Database
- **Storage**: IndexedDB per persistenza locale
- **Struttura Dati**:
```javascript
{
  gameId: string,
  moves: [{
    gameState: GameStateSnapshot,
    action: CardAction,
    reward: number,
    outcome: 'win' | 'lose' | 'draw'
  }],
  finalScore: number,
  difficulty: 'easy' | 'medium' | 'hard'
}
```

#### C. AI Learning Module
- **Algoritmo**: Q-Learning semplificato
- **Rete Neurale**: Piccola rete (2-3 layer, max 64 neuroni per layer)
- **Training**: Batch learning ogni N partite

### 3. Implementazione Tecnica

#### Fase 1: Data Collection (Settimane 1-2)
```typescript
// src/utils/ai/learning/ExperienceCollector.ts
export class ExperienceCollector {
  private experiences: Experience[] = [];
  
  recordMove(gameState: GameState, action: Card, reward: number) {
    this.experiences.push({
      state: this.encodeGameState(gameState),
      action: this.encodeAction(action),
      reward,
      timestamp: Date.now()
    });
  }
  
  private encodeGameState(state: GameState): number[] {
    // Converte lo stato del gioco in un vettore numerico
    // - Carte in mano (40 bit)
    // - Carte giocate (40 bit) 
    // - Briscola (4 bit)
    // - Posizione nel turno (2 bit)
    // - Punteggio attuale (8 bit)
    // Totale: ~94 features
  }
}
```

#### Fase 2: Simple Q-Learning (Settimane 3-4)
```typescript
// src/utils/ai/learning/QLearning.ts
export class SimpleQLearning {
  private qTable: Map<string, Map<string, number>> = new Map();
  private alpha = 0.1; // Learning rate
  private gamma = 0.9; // Discount factor
  private epsilon = 0.1; // Exploration rate
  
  getAction(state: number[], validActions: Card[]): Card {
    const stateKey = this.encodeState(state);
    
    // Epsilon-greedy strategy
    if (Math.random() < this.epsilon) {
      return this.randomAction(validActions);
    }
    
    return this.bestAction(stateKey, validActions);
  }
  
  updateQ(state: number[], action: Card, reward: number, nextState: number[]) {
    const stateKey = this.encodeState(state);
    const actionKey = this.encodeAction(action);
    
    const currentQ = this.getQ(stateKey, actionKey);
    const maxNextQ = this.getMaxQ(this.encodeState(nextState));
    
    const newQ = currentQ + this.alpha * (reward + this.gamma * maxNextQ - currentQ);
    this.setQ(stateKey, actionKey, newQ);
  }
}
```

#### Fase 3: Neural Network Integration (Settimane 5-6)
```typescript
// src/utils/ai/learning/NeuralNetwork.ts
import * as tf from '@tensorflow/tfjs';

export class LightweightNN {
  private model: tf.Sequential;
  
  constructor() {
    this.model = tf.sequential({
      layers: [
        tf.layers.dense({ inputShape: [94], units: 64, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({ units: 32, activation: 'relu' }),
        tf.layers.dense({ units: 40, activation: 'linear' }) // 40 possibili carte
      ]
    });
    
    this.model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError'
    });
  }
  
  async predict(gameState: number[]): Promise<number[]> {
    const tensor = tf.tensor2d([gameState]);
    const prediction = this.model.predict(tensor) as tf.Tensor;
    return Array.from(await prediction.data());
  }
  
  async train(experiences: Experience[]) {
    if (experiences.length < 32) return; // Minimum batch size
    
    const states = experiences.map(e => e.state);
    const targets = await this.calculateTargets(experiences);
    
    await this.model.fit(
      tf.tensor2d(states),
      tf.tensor2d(targets),
      {
        epochs: 1,
        batchSize: 32,
        verbose: 0
      }
    );
  }
}
```

### 4. Integrazione con Sistema Esistente

#### Modifica dell'AI Handler
```typescript
// src/utils/ai/aiHandler.ts
export const makeAIMove = async (
  gameState: GameState,
  playerIndex: number,
  difficulty: AIDifficulty
): Promise<Card> => {
  const validCards = getValidCards(currentPlayer.hand, gameState.leadSuit);
  
  // Usa il sistema di apprendimento se disponibile e addestrato
  if (await LearningSystem.isReady()) {
    const learnedMove = await LearningSystem.suggestMove(gameState, validCards);
    
    // Combina con strategia esistente (weighted average)
    const traditionalMove = getUnifiedAIMove(validCards, gameState, playerIndex, difficulty);
    const finalMove = LearningSystem.combineStrategies(learnedMove, traditionalMove, 0.3);
    
    // Registra l'esperienza
    ExperienceCollector.recordMove(gameState, finalMove, 0); // Reward calcolato dopo
    
    return finalMove;
  }
  
  // Fallback alla strategia esistente
  return getUnifiedAIMove(validCards, gameState, playerIndex, difficulty);
};
```

### 5. Reward System

#### Reward Immediati
- **Presa trick**: +1 punto per ogni punto nel trick
- **Presa con carta bassa**: +0.5 bonus
- **Spreco carta alta**: -0.5 penalità
- **Cooperazione con partner**: +0.3 bonus

#### Reward Finali
- **Vittoria partita**: +10 punti
- **Sconfitta**: -5 punti
- **Pareggio**: 0 punti

### 6. Ottimizzazioni per Mobile

#### Storage Efficiente
```typescript
// Compressione dati
const compressExperience = (exp: Experience): CompressedExperience => ({
  s: exp.state.map(x => Math.round(x * 100)), // Quantizzazione
  a: exp.action,
  r: Math.round(exp.reward * 10),
  t: exp.timestamp
});
```

#### Training Asincrono
```typescript
// Training in background usando Web Workers
const trainingWorker = new Worker('/workers/ai-training.js');
trainingWorker.postMessage({ experiences: recentExperiences });
```

#### Modello Compatto
- **Dimensioni**: < 100KB per il modello addestrato
- **Quantizzazione**: Int8 invece di Float32
- **Pruning**: Rimozione connessioni deboli

### 7. Timeline di Implementazione

**Settimana 1-2**: Experience Collection
- Implementare ExperienceCollector
- Integrare con game engine esistente
- Setup IndexedDB storage

**Settimana 3-4**: Q-Learning Base
- Implementare Q-Learning semplificato
- Sistema di reward
- Testing iniziale

**Settimana 5-6**: Neural Network
- Integrare TensorFlow.js
- Implementare rete neurale leggera
- Training pipeline

**Settimana 7-8**: Ottimizzazione e Testing
- Compressione modello
- Performance tuning
- Testing estensivo

### 8. Metriche di Successo

- **Win Rate**: Miglioramento del 10-15% dopo 100 partite
- **Performance**: Nessun lag percettibile durante il gameplay
- **Storage**: < 5MB di dati di apprendimento
- **Battery**: Nessun impatto significativo sulla batteria

### 9. Considerazioni Tecniche

#### Pro dell'Approccio
- ✅ Completamente offline
- ✅ Peso ridotto (TensorFlow.js lite)
- ✅ Miglioramento graduale
- ✅ Compatibile con sistema esistente

#### Contro e Mitigazioni
- ❌ **Complessità**: Mitigata da implementazione graduale
- ❌ **Overfitting**: Mitigata da dropout e regularization
- ❌ **Convergenza lenta**: Mitigata da reward shaping

### 10. Alternative Considerate

#### Lookup Tables
- **Pro**: Semplicissimo, veloce
- **Contro**: Non generalizza, memoria limitata

#### Decision Trees
- **Pro**: Interpretabile, veloce
- **Contro**: Difficile da aggiornare dinamicamente

#### Genetic Algorithms
- **Pro**: Buona esplorazione
- **Contro**: Convergenza molto lenta

## Conclusione

L'approccio Reinforcement Learning con rete neurale leggera rappresenta il miglior compromesso tra efficacia, performance e semplicità di implementazione per questo progetto. Il sistema proposto permetterà alla CPU di migliorare gradualmente le proprie decisioni mantenendo l'app leggera e reattiva.
