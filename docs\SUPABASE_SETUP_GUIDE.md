# 🔧 Guida Setup Supabase per Community AI

Questa guida ti aiuterà a configurare correttamente Supabase Storage per il sistema Community AI.

## 📋 Prerequisiti

- Account Supabase attivo
- Progetto Supabase creato
- Credenziali Supabase configurate nell'app (.env)

## 🚀 Setup Passo-Passo

### **Step 1: Crea il Bucket Storage**

1. **Accedi al Dashboard Supabase**
   - Vai su [supabase.com](https://supabase.com)
   - Accedi al tuo progetto

2. **Naviga a Storage**
   - Sidebar sinistra → **Storage**
   - Click su **Buckets**

3. **Crea Nuovo Bucket**
   - Click **"Create Bucket"**
   - Compila i campi:
     ```
     Bucket name: ai-training-data
     Public bucket: ❌ (NON selezionare)
     File size limit: 102400
     Allowed MIME types: application/json,application/gzip,text/plain
     ```
   - Click **"Create bucket"**

### **Step 2: Configura le Policy RLS**

1. **Apri SQL Editor**
   - Sidebar sinistra → **SQL Editor**
   - Click **"New query"**

2. **Esegui Script di Configurazione**
   
   Copia e incolla questo script completo:

   ```sql
   -- 🧠 COMMUNITY AI - Configurazione Storage Policy
   
   -- Policy per permettere inserimenti anonimi (upload dati)
   CREATE POLICY IF NOT EXISTS "Allow anonymous uploads to ai-training-data"
   ON storage.objects
   FOR INSERT
   WITH CHECK (
     bucket_id = 'ai-training-data'
   );
   
   -- Policy per permettere lettura anonima (per admin e debug)
   CREATE POLICY IF NOT EXISTS "Allow anonymous read access to ai-training-data"
   ON storage.objects
   FOR SELECT
   USING (
     bucket_id = 'ai-training-data'
   );
   
   -- Policy per permettere eliminazione (per pulizia dati)
   CREATE POLICY IF NOT EXISTS "Allow delete access to ai-training-data"
   ON storage.objects
   FOR DELETE
   USING (
     bucket_id = 'ai-training-data'
   );
   
   -- Tabella per statistiche di upload
   CREATE TABLE IF NOT EXISTS public.ai_training_stats (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     file_count INTEGER DEFAULT 0,
     total_size_kb INTEGER DEFAULT 0,
     last_upload TIMESTAMP WITH TIME ZONE,
     version TEXT DEFAULT '1.0',
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   
   -- Record iniziale statistiche
   INSERT INTO public.ai_training_stats (file_count, total_size_kb)
   VALUES (0, 0)
   ON CONFLICT DO NOTHING;
   
   -- RLS per tabella statistiche
   ALTER TABLE public.ai_training_stats ENABLE ROW LEVEL SECURITY;
   
   -- Policy lettura pubblica statistiche
   CREATE POLICY IF NOT EXISTS "Allow public read access to ai_training_stats"
   ON public.ai_training_stats
   FOR SELECT
   USING (true);
   
   -- Policy aggiornamento statistiche
   CREATE POLICY IF NOT EXISTS "Allow public updates to ai_training_stats"
   ON public.ai_training_stats
   FOR UPDATE
   USING (true)
   WITH CHECK (true);
   
   -- Policy inserimento statistiche
   CREATE POLICY IF NOT EXISTS "Allow public insert to ai_training_stats"
   ON public.ai_training_stats
   FOR INSERT
   WITH CHECK (true);
   
   -- Funzione per aggiornare updated_at automaticamente
   CREATE OR REPLACE FUNCTION update_updated_at_column()
   RETURNS TRIGGER AS $$
   BEGIN
       NEW.updated_at = NOW();
       RETURN NEW;
   END;
   $$ language 'plpgsql';
   
   -- Trigger per updated_at
   DROP TRIGGER IF EXISTS update_ai_training_stats_updated_at ON public.ai_training_stats;
   CREATE TRIGGER update_ai_training_stats_updated_at
       BEFORE UPDATE ON public.ai_training_stats
       FOR EACH ROW
       EXECUTE FUNCTION update_updated_at_column();
   ```

3. **Esegui lo Script**
   - Click **"Run"** (o Ctrl+Enter)
   - Verifica che non ci siano errori

### **Step 3: Verifica Configurazione**

1. **Controlla Bucket**
   - Torna a **Storage → Buckets**
   - Dovresti vedere `ai-training-data` nella lista
   - Status dovrebbe essere "Private"

2. **Controlla Tabella**
   - Vai a **Table Editor**
   - Dovresti vedere `ai_training_stats` nella lista tabelle

3. **Test nell'App**
   - Apri il pannello admin: `/admin-dashboard`
   - Login con chiave: `maraffa-admin-2024`
   - Vai al tab **"Debug"**
   - Click **"Test Completo"**
   - Dovresti vedere: ✅ "Setup completato con successo"

## ✅ Verifica Funzionamento

### **Test Upload**
1. Gioca 1-2 partite offline vs CPU
2. Torna al pannello admin
3. Tab **"Overview"** → Controlla "Partite Raccolte"
4. Tab **"Training"** → Verifica metriche aggiornate

### **Indicatori di Successo**
- ✅ Bucket `ai-training-data` visibile in Supabase
- ✅ Tabella `ai_training_stats` creata
- ✅ Test upload nel pannello admin passa
- ✅ Statistiche si aggiornano dopo le partite
- ✅ Nessun errore nelle notifiche admin

## 🚨 Risoluzione Problemi

### **Errore: "Bucket not found"**
**Causa:** Bucket non creato o nome errato
**Soluzione:** 
1. Verifica nome bucket: deve essere esattamente `ai-training-data`
2. Controlla che sia nel progetto Supabase corretto
3. Ricrea il bucket se necessario

### **Errore: "Permission denied"**
**Causa:** Policy RLS non configurate
**Soluzione:**
1. Esegui di nuovo lo script SQL completo
2. Verifica che tutte le policy siano create
3. Controlla che RLS sia abilitato

### **Errore: "new row violates row-level security policy"**
**Causa:** Policy RLS troppo restrittive
**Soluzione:**
1. Verifica che le policy permettano inserimenti anonimi
2. Controlla che `bucket_id = 'ai-training-data'` nelle policy
3. Ricrea le policy se necessario

### **Upload fallisce sempre**
**Possibili cause:**
1. **Credenziali errate:** Verifica SUPABASE_URL e SUPABASE_ANON_KEY nel .env
2. **Connessione:** Controlla connessione internet
3. **Limiti:** Verifica che i file siano sotto 100KB
4. **CORS:** Aggiungi il dominio dell'app nelle impostazioni Supabase

### **Nessun dato nel dashboard**
**Possibili cause:**
1. **Nessuna partita giocata:** Gioca almeno 1 partita offline vs CPU
2. **Raccolta disabilitata:** Verifica che Community AI sia abilitato
3. **Errori silenziosi:** Controlla console browser per errori

## 📊 Monitoraggio

### **Dashboard Supabase**
- **Storage → ai-training-data:** Vedi file caricati
- **Table Editor → ai_training_stats:** Statistiche upload
- **Logs:** Errori e attività

### **Pannello Admin App**
- **Notifiche:** Problemi automaticamente rilevati
- **Tab Debug:** Test e diagnostica completa
- **Tab Training:** Metriche e analisi dati

## 🔒 Sicurezza

### **Dati Raccolti (Anonimi)**
- ✅ Solo dati di gioco (mosse, punteggi, timing)
- ✅ Nessuna informazione personale
- ✅ Nessun ID utente o identificativo

### **Accesso Dati**
- 🔒 Bucket privato (non pubblico)
- 🔒 Policy RLS configurate
- 🔒 Solo app autorizzata può scrivere
- 🔒 Admin può leggere per analisi

## 🎯 Prossimi Passi

Una volta completato il setup:

1. **Raccogli Dati:** Gioca partite per raccogliere dati training
2. **Monitora:** Usa pannello admin per monitorare progresso
3. **Configura AI:** Quando hai 100+ partite, attiva AI addestrata
4. **Ottimizza:** Usa analytics per migliorare configurazioni

## 📞 Supporto

Se hai problemi:
1. **Pannello Admin:** Tab Debug per diagnostica automatica
2. **Console Browser:** F12 → Console per errori dettagliati
3. **Logs Supabase:** Dashboard → Logs per errori server
4. **Documentazione:** Consulta questa guida e quella del pannello admin

---

**✅ Setup completato con successo quando:**
- Bucket creato e visibile
- Policy configurate correttamente  
- Test upload passa nel pannello admin
- Statistiche si aggiornano dopo le partite
