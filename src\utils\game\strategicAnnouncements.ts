/**
 * 🗣️ Sistema Dichiarazioni Strategiche - Parole
 *
 * Implementa la comunicazione strategica tra compagni attraverso:
 * - Busso: "Prendi questa mano e la prossima gioca ancora questo seme che prendo io!"
 * - Striscio: "Ne ho ancora una di questo seme"
 * - Volo: "Non ho più carte di questo seme, ora posso tagliare con briscola"
 *
 * 🚫 REGOLE TEMPORALI:
 * - Gli annunci strategici sono consentiti SOLO nei primi 5 turni di una mano
 * - Dopo il turno 5, non è più possibile fare dichiarazioni strategiche
 *
 * 🎯 REGOLE BUSSO:
 * - Quando un compagno chiama "busso", il teammate deve cercare di prendere la presa
 * - Priorità alla carta più alta del seme dichiarato, poi briscole se necessario
 */

import { Card, Suit, Rank } from "./cardUtils";
import { GameState, Player } from "./gameLogic";

export type StrategicAnnouncement = "busso" | "striscio" | "volo" | null;

export interface AnnouncementInfo {
  type: StrategicAnnouncement;
  playerIndex: number;
  suit: Suit;
  trickNumber: number;
}

// Interfaccia per la memoria AI delle dichiarazioni
interface AnnouncementMemory extends AnnouncementInfo {
  timestamp: number;
}

// Interfaccia per le obbligazioni di busso
interface BussoObligation {
  playerIndex: number;
  suit: Suit;
  trickNumber: number;
  mustReturnInNextTrick: boolean;
  timestamp: number;
}

// Interfaccia per estendere Window con proprietà AI
interface ExtendedWindow extends Window {
  aiMemory?: {
    strategicAnnouncements?: AnnouncementMemory[];
    bussoObligations?: BussoObligation[];
  };
}

/**
 * 🎯 Analizza se un giocatore dovrebbe fare una dichiarazione strategica
 * Solo il primo giocatore del turno può fare dichiarazioni
 */
export const shouldMakeStrategicAnnouncement = (
  player: Player,
  gameState: GameState,
  intendedCard: Card
): StrategicAnnouncement => {
  // Solo il primo giocatore del turno può dichiarare
  if (gameState.currentTrick.length > 0) {
    return null;
  }
  // Non dichiarare nel primo turno (troppo presto)
  if (gameState.trickNumber === 1) {
    return null;
  }

  // 🚫 REGOLA: Annunci strategici consentiti solo nei primi 5 turni
  if (gameState.trickNumber > 5) {
    return null;
  }

  const suitToAnalyze = intendedCard.suit;
  const cardsInSuit = player.hand.filter((card) => card.suit === suitToAnalyze);
  const trumpCards = gameState.trumpSuit
    ? player.hand.filter((card) => card.suit === gameState.trumpSuit)
    : [];
  // 🚫 VOLO: "Non ho più carte di questo seme, ora posso tagliare con briscola"
  // Condizioni: questa è l'ULTIMA carta di questo seme che ho in mano E ho briscole per tagliare
  if (cardsInSuit.length === 1 && trumpCards.length > 0) {
    return "volo";
  }

  // 📢 BUSSO: "Prendi questa mano e la prossima gioca ancora questo seme che prendo io!"
  // 🎯 REGOLA CORRETTA: sto giocando una carta di un seme di cui ho il 2, ma NON sto giocando il 2 stesso
  const hasTwoOfThisSuit = cardsInSuit.some((card) => card.rank === Rank.Two);
  const hasThreeOfThisSuit = cardsInSuit.some(
    (card) => card.rank === Rank.Three
  );
  const hasAceOfThisSuit = cardsInSuit.some((card) => card.rank === Rank.Ace);
  const isPlayingSuitWithTwo =
    hasTwoOfThisSuit &&
    intendedCard.suit === suitToAnalyze &&
    intendedCard.rank !== Rank.Two; // 🎯 NON deve essere il 2 stesso!

  if (
    isPlayingSuitWithTwo &&
    !hasThreeOfThisSuit &&
    !hasAceOfThisSuit &&
    cardsInSuit.length >= 2
  ) {
    console.log(
      `[BUSSO] 🎯 Gioco ${intendedCard.rank} di ${intendedCard.suit} - ho il 2 dello stesso seme!`
    );
    return "busso";
  }

  // 🎯 STRISCIO: "Ne ho ancora una di questo seme"
  // Condizioni: ho esattamente 2 carte di questo seme (dopo aver giocato questa ne avrò 1)
  if (cardsInSuit.length === 2) {
    return "striscio";
  }

  return null;
};

/**
 * 🧠 Valuta strategicamente quale dichiarazione fare in base alla situazione
 */
export const evaluateStrategicAnnouncement = (
  player: Player,
  gameState: GameState,
  intendedCard: Card,
  difficulty: "easy" | "medium" | "hard"
): StrategicAnnouncement => {
  const basicAnnouncement = shouldMakeStrategicAnnouncement(
    player,
    gameState,
    intendedCard
  );

  if (!basicAnnouncement) {
    return null;
  }

  // Probabilità di dichiarare in base alla difficoltà
  const announcementProbability = {
    easy: 0.4, // 40% probabilità
    medium: 0.7, // 70% probabilità
    hard: 0.9, // 90% probabilità
  };

  if (Math.random() > announcementProbability[difficulty]) {
    return null; // Non dichiara
  }

  // Valutazione strategica specifica per tipo di dichiarazione
  switch (basicAnnouncement) {
    case "busso":
      // BUSSO è molto strategico - fallo solo se ha senso
      return evaluateBussoStrategy(player, gameState, intendedCard)
        ? "busso"
        : null;

    case "volo":
      // VOLO è informativo - quasi sempre utile dichiararlo
      return Math.random() < 0.8 ? "volo" : null;

    case "striscio":
      // STRISCIO è neutro - moderatamente utile
      return Math.random() < 0.6 ? "striscio" : null;

    default:
      return null;
  }
};

/**
 * 🎯 Valuta se la strategia BUSSO è efficace
 */
const evaluateBussoStrategy = (
  player: Player,
  gameState: GameState,
  intendedCard: Card
): boolean => {
  const suitCards = player.hand.filter(
    (card) => card.suit === intendedCard.suit
  );
  const hasTwo = suitCards.some((card) => card.rank === Rank.Two);
  // Fix: annulla SEMPRE se non hai il 2 del seme, anche se chiamata erroneamente
  if (!hasTwo) return false;

  // Non dichiarare BUSSO se siamo oltre i primi 5 turni (regola aggiornata)
  if (gameState.trickNumber > 5) return false;

  // Non dichiarare se il compagno è molto lontano nella rotazione
  const teammateIndex = gameState.players.findIndex(
    (p) => p.team === player.team && p.id !== player.id
  );

  if (teammateIndex === -1) return false;

  // Più efficace se il compagno gioca presto nel turno
  const currentPlayerIndex = gameState.currentPlayer;
  const distanceToTeammate = (teammateIndex - currentPlayerIndex + 4) % 4;

  return distanceToTeammate <= 2; // Compagno gioca entro 2 posizioni
};

/**
 * 📝 Aggiorna la memoria AI con le informazioni dalla dichiarazione
 */
export const updateAIMemoryWithAnnouncement = (
  announcement: AnnouncementInfo,
  gameState: GameState
): void => {
  console.log(
    `[STRATEGIC ANNOUNCEMENTS] 📢 ${
      gameState.players[announcement.playerIndex].name
    } dichiara: ${announcement.type.toUpperCase()}`
  );

  // Aggiorna la memoria globale dell'AI
  if (typeof window !== "undefined" && (window as ExtendedWindow).aiMemory) {
    const aiMemory = (window as ExtendedWindow).aiMemory!;

    if (!aiMemory.strategicAnnouncements) {
      aiMemory.strategicAnnouncements = [];
    }

    aiMemory.strategicAnnouncements.push({
      ...announcement,
      timestamp: Date.now(),
    });

    // Mantieni solo le dichiarazioni recenti (ultimi 3 turni)
    const currentTrick = gameState.trickNumber;
    aiMemory.strategicAnnouncements = aiMemory.strategicAnnouncements.filter(
      (ann: AnnouncementMemory) => currentTrick - ann.trickNumber <= 3
    );
  }
};

/**
 * 🎯 Traccia quando un giocatore prende dopo un busso del compagno
 * Questo è importante per la regola che obbliga a tornare nel seme
 */
export const trackBussoTrickWin = (
  gameState: GameState,
  trickWinner: number,
  trickCards: Card[]
): void => {
  if (
    typeof window !== "undefined" &&
    (window as ExtendedWindow).aiMemory?.strategicAnnouncements
  ) {
    const aiMemory = (window as ExtendedWindow).aiMemory!;
    const winnerPlayer = gameState.players[trickWinner];

    // Cerca se c'è stato un busso del compagno in questo turno
    const currentTrickBusso = aiMemory.strategicAnnouncements.find(
      (ann: AnnouncementMemory) =>
        ann.type === "busso" &&
        ann.trickNumber === gameState.trickNumber &&
        gameState.players[ann.playerIndex].team === winnerPlayer.team &&
        ann.playerIndex !== trickWinner
    );

    if (currentTrickBusso) {
      console.log(
        `[BUSSO TRACKING] 🎯 ${winnerPlayer.name} ha preso dopo busso del compagno (${currentTrickBusso.suit}). Dovrà tornare in quel seme!`
      );

      // Aggiungi un marker speciale per ricordare che questo giocatore deve tornare nel seme
      if (!aiMemory.bussoObligations) {
        aiMemory.bussoObligations = [];
      }

      aiMemory.bussoObligations.push({
        playerIndex: trickWinner,
        suit: currentTrickBusso.suit,
        trickNumber: gameState.trickNumber,
        mustReturnInNextTrick: true,
        timestamp: Date.now(),
      });
    }
  }
};

/**
 * 🤝 Ottieni le dichiarazioni rilevanti per la strategia corrente
 */
export const getRelevantAnnouncements = (
  gameState: GameState,
  playerIndex: number
): AnnouncementInfo[] => {
  if (typeof window === "undefined" || !(window as ExtendedWindow).aiMemory) {
    return [];
  }

  const aiMemory = (window as ExtendedWindow).aiMemory!;
  if (!aiMemory.strategicAnnouncements) {
    return [];
  }

  const currentTrick = gameState.trickNumber;

  return aiMemory.strategicAnnouncements.filter((ann: AnnouncementMemory) => {
    // Solo dichiarazioni recenti (ultimi 2 turni)
    if (currentTrick - ann.trickNumber > 2) return false;

    // Filtra in base alla relazione con il giocatore
    const announcer = gameState.players[ann.playerIndex];
    const currentPlayer = gameState.players[playerIndex];

    // Se siamo compagni, tutte le dichiarazioni sono rilevanti
    if (announcer.team === currentPlayer.team) return true;

    // Se siamo avversari, solo alcune dichiarazioni ci interessano
    return ann.type === "volo"; // Gli avversari possono intuire il "volo"
  });
};

/**
 * 🎮 Applica la strategia basata sulle dichiarazioni del compagno
 */
export const applyTeammateAnnouncementStrategy = (
  availableCards: Card[],
  gameState: GameState,
  playerIndex: number
): {
  recommendedCards: Card[];
  strategy: string;
} => {
  const announcements = getRelevantAnnouncements(gameState, playerIndex);
  const currentPlayer = gameState.players[playerIndex];

  // Filtra solo le dichiarazioni del compagno
  const teammateAnnouncements = announcements.filter(
    (ann) =>
      gameState.players[ann.playerIndex].team === currentPlayer.team &&
      ann.playerIndex !== playerIndex
  );

  if (teammateAnnouncements.length === 0) {
    return {
      recommendedCards: availableCards,
      strategy: "Nessuna dichiarazione del compagno",
    };
  }

  const latestAnnouncement =
    teammateAnnouncements[teammateAnnouncements.length - 1];

  switch (latestAnnouncement.type) {
    case "busso":
      // Il compagno vuole che prendiamo e rigiochiamo il suo seme
      return handleBussoStrategy(availableCards, gameState, latestAnnouncement);

    case "striscio":
      // Il compagno ha ancora una carta di quel seme
      return handleStriscioStrategy(
        availableCards,
        gameState,
        latestAnnouncement
      );

    case "volo":
      // Il compagno può tagliare quel seme
      return handleVoloStrategy(availableCards, gameState, latestAnnouncement);

    default:
      return {
        recommendedCards: availableCards,
        strategy: "Dichiarazione non riconosciuta",
      };
  }
};

/**
 * 📢 Gestisce la strategia per dichiarazione BUSSO del compagno
 * REGOLA MIGLIORATA: Il compagno deve giocare la carta più alta del seme SOLO SE PUÒ VINCERE
 */
const handleBussoStrategy = (
  availableCards: Card[],
  gameState: GameState,
  announcement: AnnouncementInfo
): { recommendedCards: Card[]; strategy: string } => {
  const announcementSuit = announcement.suit;
  const currentTrick = gameState.currentTrick || [];

  // Helper function per determinare l'ordine delle carte
  const getCardOrder = (card: Card): number => {
    const order: Record<string, number> = {
      "3": 10,
      "2": 9,
      A: 8,
      K: 7,
      H: 6,
      J: 5,
      "7": 4,
      "6": 3,
      "5": 2,
      "4": 1,
    };
    return order[card.rank] || 0;
  };

  // Helper function per verificare se una carta può vincere il trick corrente
  const canWinTrick = (card: Card): boolean => {
    if (currentTrick.length === 0) return true; // Primo a giocare

    const leadSuit = currentTrick[0].suit;
    const isTrump = card.suit === gameState.trumpSuit;
    const isLeadSuit = card.suit === leadSuit;

    // Se la carta è briscola e il seme di apertura non è briscola, può vincere
    if (isTrump && leadSuit !== gameState.trumpSuit) return true;

    // Se la carta non è del seme di apertura e non è briscola, non può vincere
    if (!isLeadSuit && !isTrump) return false;

    // Controlla se può battere le carte già giocate
    for (const playedCard of currentTrick) {
      const playedIsTrump = playedCard.suit === gameState.trumpSuit;
      const playedIsLeadSuit = playedCard.suit === leadSuit;

      // Se la carta giocata è briscola e la nostra non lo è, non possiamo vincere
      if (playedIsTrump && !isTrump) return false;

      // Se entrambe sono briscole, confronta la forza
      if (playedIsTrump && isTrump) {
        if (getCardOrder(playedCard) >= getCardOrder(card)) return false;
      }

      // Se entrambe sono del seme di apertura, confronta la forza
      if (playedIsLeadSuit && isLeadSuit && !playedIsTrump && !isTrump) {
        if (getCardOrder(playedCard) >= getCardOrder(card)) return false;
      }
    }

    return true;
  };

  // 🎯 REGOLA BUSSO CORRETTA: Cerca carte del seme che possono vincere
  const announcementSuitCards = availableCards.filter(
    (card) => card.suit === announcementSuit
  );

  if (announcementSuitCards.length > 0) {
    // Filtra solo le carte che possono effettivamente vincere il trick
    const winningSuitCards = announcementSuitCards.filter(canWinTrick);

    if (winningSuitCards.length > 0) {
      // Ordina per forza decrescente e prendi la più forte che può vincere
      const sortedWinningCards = winningSuitCards.sort((a, b) => {
        return getCardOrder(b) - getCardOrder(a);
      });

      const strongestWinningCard = sortedWinningCards[0];

      return {
        recommendedCards: [strongestWinningCard],
        strategy: `BUSSO: Prendo con ${strongestWinningCard.rank} di ${announcementSuit} (carta più forte che può vincere)`,
      };
    } else {
      // Se ho carte del seme ma non possono vincere, non le gioco per busso
      console.log(
        `[BUSSO] Ho carte di ${announcementSuit} ma non possono vincere il trick`
      );
    }
  }

  // Priorità 2: Se non abbiamo carte del seme che possono vincere, usiamo briscole
  const trumpCards = availableCards.filter(
    (card) => card.suit === gameState.trumpSuit
  );
  if (trumpCards.length > 0) {
    // Filtra solo le briscole che possono vincere il trick
    const winningTrumps = trumpCards.filter(canWinTrick);

    if (winningTrumps.length > 0) {
      // Usa la briscola più debole che può vincere
      const sortedWinningTrumps = winningTrumps.sort((a, b) => {
        return getCardOrder(a) - getCardOrder(b); // Più debole prima
      });

      return {
        recommendedCards: [sortedWinningTrumps[0]],
        strategy: `BUSSO: Prendo con briscola ${sortedWinningTrumps[0].rank} (non ho carte vincenti del seme ${announcementSuit})`,
      };
    }
  }

  // Priorità 3: Se non possiamo prendere, cerchiamo comunque di cooperare
  const strongCards = availableCards.filter((card) => {
    if (card.suit === gameState.trumpSuit) return true; // Briscole
    if (card.suit === announcementSuit) {
      return (
        card.rank === Rank.Three ||
        card.rank === Rank.Two ||
        card.rank === Rank.Ace
      );
    }
    return false;
  });

  if (strongCards.length > 0) {
    return {
      recommendedCards: strongCards,
      strategy: `BUSSO: Prendo la mano per collaborare con il compagno (seme: ${announcementSuit})`,
    };
  }

  return {
    recommendedCards: availableCards,
    strategy: "BUSSO: Non posso prendere la mano",
  };
};

/**
 * 🎯 Gestisce la strategia per dichiarazione STRISCIO del compagno
 */
const handleStriscioStrategy = (
  availableCards: Card[],
  gameState: GameState,
  announcement: AnnouncementInfo
): { recommendedCards: Card[]; strategy: string } => {
  // Il compagno ha ancora una carta di quel seme - giochiamo normalmente
  return {
    recommendedCards: availableCards,
    strategy: `STRISCIO: Il compagno ha ancora carte in ${announcement.suit}`,
  };
};

/**
 * 🚁 Gestisce la strategia per dichiarazione VOLO del compagno
 */
const handleVoloStrategy = (
  availableCards: Card[],
  gameState: GameState,
  announcement: AnnouncementInfo
): { recommendedCards: Card[]; strategy: string } => {
  const announcementSuit = announcement.suit;

  // Se abbiamo carte di quel seme, consideriamo che il compagno può tagliare
  const suitCards = availableCards.filter(
    (card) => card.suit === announcementSuit
  );

  if (suitCards.length > 0 && gameState.leadSuit === announcementSuit) {
    // Possiamo dare punti al compagno sapendo che taglierà
    const valueCards = suitCards.filter(
      (card) =>
        card.rank === Rank.Ace ||
        card.rank === Rank.King ||
        card.rank === Rank.Horse ||
        card.rank === Rank.Jack
    );

    if (valueCards.length > 0) {
      return {
        recommendedCards: valueCards,
        strategy: `VOLO: Do punti al compagno che tagliarà (seme: ${announcementSuit})`,
      };
    }
  }

  return {
    recommendedCards: availableCards,
    strategy: `VOLO: Il compagno può tagliare ${announcementSuit}`,
  };
};
