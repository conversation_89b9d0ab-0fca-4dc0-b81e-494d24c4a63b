/**
 * 📖 ADMIN GUIDE COMPONENT
 * Guida completa per l'utilizzo del pannello admin
 */

import React, { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  X,
  BookOpen,
  Settings,
  Database,
  Brain,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Info,
  Zap,
  Shield,
  Download,
  Upload,
  Trash2,
} from "lucide-react";

interface AdminGuideProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AdminGuide: React.FC<AdminGuideProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl flex items-center gap-2">
              <BookOpen className="w-6 h-6 text-blue-600" />
              📖 Guida Pannello Admin Community AI
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-7">
              <TabsTrigger value="overview">Panoramica</TabsTrigger>
              <TabsTrigger value="setup">Setup</TabsTrigger>
              <TabsTrigger value="training">Training</TabsTrigger>
              <TabsTrigger value="auto-training">Auto-Training</TabsTrigger>
              <TabsTrigger value="ai-config">AI Config</TabsTrigger>
              <TabsTrigger value="data">Gestione Dati</TabsTrigger>
              <TabsTrigger value="troubleshooting">Risoluzione</TabsTrigger>
            </TabsList>

            {/* Tab Panoramica */}
            <TabsContent value="overview" className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>Benvenuto nel pannello admin Community AI!</strong>
                  <br />
                  Questo sistema raccoglie dati dalle partite per addestrare
                  un'AI che migliorerà le decisioni CPU.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <BarChart3 className="w-5 h-5" />
                      Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-sm">
                      • Statistiche sistema in tempo reale
                    </p>
                    <p className="text-sm">• Stato raccolta dati</p>
                    <p className="text-sm">• Notifiche automatiche</p>
                    <p className="text-sm">• Controllo sessione admin</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Brain className="w-5 h-5" />
                      Training
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-sm">• Progresso addestramento AI</p>
                    <p className="text-sm">• Metriche qualità dati</p>
                    <p className="text-sm">• Analisi pattern di gioco</p>
                    <p className="text-sm">• Grafici interattivi</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Settings className="w-5 h-5" />
                      Impostazioni
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-sm">• Configurazione AI addestrata</p>
                    <p className="text-sm">
                      • Toggle attivazione/disattivazione
                    </p>
                    <p className="text-sm">• Percentuale utilizzo</p>
                    <p className="text-sm">• Soglie confidenza</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Database className="w-5 h-5" />
                      Gestione Dati
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-sm">• Export dati in JSON/CSV</p>
                    <p className="text-sm">• Backup automatici</p>
                    <p className="text-sm">• Pulizia dati obsoleti</p>
                    <p className="text-sm">• Statistiche storage</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Tab Setup */}
            <TabsContent value="setup" className="space-y-4">
              <Alert variant="warning">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Setup Supabase Richiesto!</strong>
                  <br />
                  Prima di utilizzare il sistema, configura Supabase Storage.
                </AlertDescription>
              </Alert>

              <Card>
                <CardHeader>
                  <CardTitle>🔧 Configurazione Supabase</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <Badge variant="outline" className="mt-1">
                        1
                      </Badge>
                      <div>
                        <p className="font-medium">Crea Bucket Storage</p>
                        <p className="text-sm text-gray-600">
                          Dashboard Supabase → Storage → Create Bucket
                          <br />
                          Nome:{" "}
                          <code className="bg-gray-100 px-1 rounded">
                            ai-training-data
                          </code>
                          <br />
                          Public:{" "}
                          <code className="bg-gray-100 px-1 rounded">
                            false
                          </code>
                          <br />
                          File size limit:{" "}
                          <code className="bg-gray-100 px-1 rounded">
                            102400
                          </code>{" "}
                          (100KB)
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <Badge variant="outline" className="mt-1">
                        2
                      </Badge>
                      <div>
                        <p className="font-medium">Esegui Script SQL</p>
                        <p className="text-sm text-gray-600">
                          SQL Editor → Esegui il contenuto di{" "}
                          <code className="bg-gray-100 px-1 rounded">
                            supabase/storage-setup.sql
                          </code>
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <Badge variant="outline" className="mt-1">
                        3
                      </Badge>
                      <div>
                        <p className="font-medium">Testa Configurazione</p>
                        <p className="text-sm text-gray-600">
                          Tab Debug → "Test Completo" per verificare che tutto
                          funzioni
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Verifica Setup:</strong> Se vedi "✅ Upload riuscito"
                  nel tab Debug, la configurazione è corretta!
                </AlertDescription>
              </Alert>
            </TabsContent>

            {/* Tab Training */}
            <TabsContent value="training" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>📊 Dashboard Training</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <p className="font-medium">🎯 Stato Training</p>
                      <p className="text-sm text-gray-600">
                        Mostra il progresso dell'addestramento AI con fasi:
                        <br />• <Badge variant="secondary">Raccolta</Badge> -
                        Raccogliendo dati (0-50 partite)
                        <br />• <Badge variant="outline">Preprocessing</Badge> -
                        Preparazione dati (50-100 partite)
                        <br />• <Badge variant="default">Training</Badge> -
                        Addestramento attivo (100-500 partite)
                        <br />• <Badge variant="default">Pronto</Badge> - AI
                        pronta per l'uso (500+ partite)
                      </p>
                    </div>

                    <div>
                      <p className="font-medium">📈 Metriche Principali</p>
                      <p className="text-sm text-gray-600">
                        • <strong>Partite Totali:</strong> Numero file raccolti
                        <br />• <strong>Mosse Totali:</strong> Somma di tutte le
                        mosse registrate
                        <br />• <strong>Durata Media:</strong> Tempo medio per
                        partita
                        <br />• <strong>Qualità Dati:</strong> Percentuale
                        partite complete
                      </p>
                    </div>

                    <div>
                      <p className="font-medium">🎮 Analisi Pattern</p>
                      <p className="text-sm text-gray-600">
                        • <strong>Win Rate per Difficoltà:</strong> Grafico a
                        barre colorato
                        <br />• <strong>Carte Più Giocate:</strong> Top 5 con
                        win rate
                        <br />• <strong>Strategie Vincenti:</strong> Pattern
                        identificati automaticamente
                        <br />• <strong>Errori Comuni:</strong> Mosse da evitare
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Tab Auto-Training */}
            <TabsContent value="auto-training" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>🤖 Sistema Auto-Training CPU vs CPU</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert>
                    <Zap className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Novità!</strong> Sistema automatico per
                      raccogliere dati di training di alta qualità senza
                      intervento umano.
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-3">
                    <div>
                      <p className="font-medium">🎯 Come Funziona</p>
                      <p className="text-sm text-gray-600">
                        • 4 CPU giocano automaticamente tra loro
                        <br />
                        • Modalità "Maestro" per decisioni di alta qualità
                        <br />
                        • Raccolta dati automatica e upload a Supabase
                        <br />• Monitoraggio in tempo reale del progresso
                      </p>
                    </div>

                    <div>
                      <p className="font-medium">⚙️ Configurazione</p>
                      <p className="text-sm text-gray-600">
                        • <strong>Partite Target:</strong> Numero di partite da
                        giocare (1-1000)
                        <br />• <strong>Velocità:</strong> Millisecondi tra le
                        mosse (0-2000ms)
                        <br />• <strong>Difficoltà:</strong>{" "}
                        Principiante/Esperto/Maestro
                        <br />• <strong>Auto Upload:</strong> Caricamento
                        automatico dati
                      </p>
                    </div>

                    <div>
                      <p className="font-medium">📊 Monitoraggio</p>
                      <p className="text-sm text-gray-600">
                        • <strong>Progresso Real-time:</strong> Partite
                        completate/target
                        <br />• <strong>Upload Status:</strong>{" "}
                        Successi/fallimenti caricamento
                        <br />• <strong>Statistiche:</strong> Mosse totali,
                        tempo rimanente
                        <br />• <strong>Log Attività:</strong> Cronologia
                        dettagliata eventi
                      </p>
                    </div>

                    <div>
                      <p className="font-medium">🚀 Vantaggi</p>
                      <p className="text-sm text-gray-600">
                        • <strong>Qualità Costante:</strong> CPU gioca sempre al
                        meglio
                        <br />• <strong>Velocità:</strong> Raccolta dati 24/7
                        senza pause
                        <br />• <strong>Controllo:</strong> Configurazione
                        precisa difficoltà
                        <br />• <strong>Efficienza:</strong> Nessun intervento
                        umano richiesto
                      </p>
                    </div>

                    <div>
                      <p className="font-medium">⏱️ Tempi Stimati</p>
                      <p className="text-sm text-gray-600">
                        • <strong>100 partite:</strong> ~30-60 minuti (velocità
                        500ms)
                        <br />• <strong>500 partite:</strong> ~3-5 ore (velocità
                        500ms)
                        <br />• <strong>1000 partite:</strong> ~6-10 ore
                        (velocità 500ms)
                        <br />• <strong>Velocità 0ms:</strong> Massima velocità
                        possibile
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Tab AI Config */}
            <TabsContent value="ai-config" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>🤖 Configurazione AI Addestrata</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert>
                    <Zap className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Importante:</strong> Attiva l'AI addestrata solo
                      dopo aver raccolto almeno 100 partite!
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-3">
                    <div>
                      <p className="font-medium">⚡ Abilita AI Addestrata</p>
                      <p className="text-sm text-gray-600">
                        Toggle principale per attivare/disattivare l'uso dell'AI
                        addestrata nelle decisioni CPU.
                      </p>
                    </div>

                    <div>
                      <p className="font-medium">📊 Percentuale Utilizzo</p>
                      <p className="text-sm text-gray-600">
                        Slider 0-100% per controllare quante decisioni CPU
                        useranno l'AI addestrata vs logica tradizionale.
                        <br />
                        Consigliato: inizia con 25-50% e aumenta gradualmente.
                      </p>
                    </div>

                    <div>
                      <p className="font-medium">🎯 Soglia Confidenza</p>
                      <p className="text-sm text-gray-600">
                        Confidenza minima (0.0-1.0) richiesta per usare una
                        decisione AI.
                        <br />
                        Se sotto soglia, usa fallback alla logica tradizionale.
                      </p>
                    </div>

                    <div>
                      <p className="font-medium">🔄 Fallback Tradizionale</p>
                      <p className="text-sm text-gray-600">
                        Se abilitato, usa AI tradizionale quando quella
                        addestrata fallisce.
                        <br />
                        <strong>Raccomandato:</strong> Sempre abilitato per
                        stabilità.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Tab Gestione Dati */}
            <TabsContent value="data" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>📁 Gestione Dati</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Download className="w-4 h-4 text-blue-500" />
                        <p className="font-medium">Export Dati</p>
                      </div>
                      <p className="text-sm text-gray-600">
                        Scarica statistiche e analytics in formato JSON. Include
                        metriche, pattern e configurazioni.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Upload className="w-4 h-4 text-green-500" />
                        <p className="font-medium">Crea Backup</p>
                      </div>
                      <p className="text-sm text-gray-600">
                        Crea una copia di sicurezza di tutti i dati raccolti.
                        Utile prima di operazioni di pulizia.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Trash2 className="w-4 h-4 text-red-500" />
                        <p className="font-medium">Pulisci Dati</p>
                      </div>
                      <p className="text-sm text-gray-600">
                        Elimina file più vecchi di 6 mesi. Mantiene sempre
                        minimo 100 file più recenti.
                      </p>
                    </div>
                  </div>

                  <Alert variant="warning">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Attenzione:</strong> Le operazioni di pulizia sono
                      irreversibili. Crea sempre un backup prima!
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Tab Troubleshooting */}
            <TabsContent value="troubleshooting" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>🔧 Risoluzione Problemi</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <p className="font-medium text-red-600">
                        ❌ "Bucket not found"
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Soluzione:</strong> Crea manualmente il bucket
                        nel dashboard Supabase:
                        <br />
                        Storage → Create Bucket → Nome: "ai-training-data" →
                        Public: false
                      </p>
                    </div>

                    <div>
                      <p className="font-medium text-red-600">
                        ❌ "Permission denied"
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Soluzione:</strong> Esegui lo script SQL per le
                        policy:
                        <br />
                        SQL Editor → Copia contenuto da
                        "supabase/storage-setup.sql" → Esegui
                      </p>
                    </div>

                    <div>
                      <p className="font-medium text-yellow-600">
                        ⚠️ "Upload fallisce sempre"
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Soluzioni:</strong>
                        <br />
                        1. Verifica credenziali Supabase nel file .env
                        <br />
                        2. Controlla connessione internet
                        <br />
                        3. Usa tab Debug → "Test Completo" per diagnostica
                      </p>
                    </div>

                    <div>
                      <p className="font-medium text-yellow-600">
                        ⚠️ "Nessun dato nel dashboard"
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Cause possibili:</strong>
                        <br />
                        1. Nessuna partita giocata ancora
                        <br />
                        2. Raccolta dati non attiva
                        <br />
                        3. Errori di upload (controlla notifiche)
                      </p>
                    </div>

                    <div>
                      <p className="font-medium text-blue-600">
                        ℹ️ Come testare il sistema
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Procedura:</strong>
                        <br />
                        1. Tab Debug → "Test Completo"
                        <br />
                        2. Gioca 1-2 partite offline vs CPU
                        <br />
                        3. Torna al pannello admin e verifica statistiche
                        <br />
                        4. Controlla notifiche per eventuali problemi
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
