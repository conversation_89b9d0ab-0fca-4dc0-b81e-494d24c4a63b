/**
 * 🤖 AUTO TRAINING SYSTEM
 * Sistema per far giocare 4 CPU in automatico per raccogliere dati di training
 */

import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Play,
  Pause,
  Square,
  BarChart3,
  Zap,
  Clock,
  Trophy,
  Brain,
  Settings,
  RefreshCw,
} from "lucide-react";

import { initializeGameState, makeMove } from "@/utils/game/gameLogic";
import { gameDataCollector } from "@/services/gameDataCollector";
import { collectGameData } from "@/services/communityAIService";
import { getCommunityAIStats } from "@/services/communityAIService";
import { getAIMove, AIDifficulty } from "@/utils/ai";
import {
  getFastTrainingMove,
  simulateTeamCommunication,
} from "@/utils/ai/fastTrainingAI";
import { Card as GameCard } from "@/utils/game/cardUtils";

interface AutoTrainingStats {
  gamesPlayed: number;
  gamesCompleted: number;
  totalMoves: number;
  averageGameDuration: number;
  successfulUploads: number;
  failedUploads: number;
  isRunning: boolean;
  currentGameProgress: number;
  estimatedTimeRemaining: number;
}

interface AutoTrainingConfig {
  targetGames: number;
  gameSpeed: number; // ms tra le mosse
  difficulty: "easy" | "medium" | "hard";
  maxConcurrentGames: number;
  autoUpload: boolean;
}

export const AutoTrainingSystem: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [stats, setStats] = useState<AutoTrainingStats>({
    gamesPlayed: 0,
    gamesCompleted: 0,
    totalMoves: 0,
    averageGameDuration: 0,
    successfulUploads: 0,
    failedUploads: 0,
    isRunning: false,
    currentGameProgress: 0,
    estimatedTimeRemaining: 0,
  });

  const [config, setConfig] = useState<AutoTrainingConfig>({
    targetGames: 100,
    gameSpeed: 0, // 0ms = velocità massima per training
    difficulty: "hard", // Usa difficoltà Maestro per dati di qualità
    maxConcurrentGames: 1,
    autoUpload: true,
  });

  const [currentGame, setCurrentGame] = useState<any>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs((prev) => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]); // Mantieni solo 50 log
  };

  const updateStats = (updates: Partial<AutoTrainingStats>) => {
    setStats((prev) => ({ ...prev, ...updates }));
  };

  const playAutomaticGame = async (): Promise<boolean> => {
    try {
      addLog("🎮 Iniziando nuova partita automatica CPU vs CPU");

      // Inizializza partita con 4 CPU
      const gameState = initializeGameState(31);
      gameState.players = gameState.players.map((player, index) => ({
        ...player,
        isHuman: false, // Tutti CPU
        name: `CPU ${index + 1}`,
        difficulty: config.difficulty,
      }));

      // Inizia raccolta dati
      gameDataCollector.startCollection(gameState);
      setCurrentGame(gameState);

      let currentState = { ...gameState };
      let moveCount = 0;
      const gameStartTime = Date.now();

      // Loop di gioco automatico
      while (currentState.gamePhase !== "gameOver") {
        const currentPlayer = currentState.currentPlayer;

        // Ottieni mossa AI veloce per il giocatore corrente
        const aiDifficulty =
          config.difficulty === "easy"
            ? AIDifficulty.EASY
            : config.difficulty === "medium"
            ? AIDifficulty.MEDIUM
            : AIDifficulty.HARD;

        const fastMove = getFastTrainingMove(
          currentState,
          currentPlayer,
          aiDifficulty
        );

        if (!fastMove || !fastMove.card) {
          addLog(
            `⚠️ Errore: AI non ha trovato mossa valida per giocatore ${currentPlayer}`
          );
          break;
        }

        // Simula comunicazione tra compagni
        if (fastMove.communication) {
          simulateTeamCommunication(
            currentState,
            currentPlayer,
            fastMove.communication
          );
        }

        // Registra la mossa per il training (con informazioni aggiuntive)
        gameDataCollector.recordMove(
          currentState,
          currentPlayer,
          fastMove.card,
          currentState.players[currentPlayer].hand,
          currentState.currentTrick,
          {
            communication: fastMove.communication,
            reasoning: fastMove.reasoning,
            aiDifficulty: config.difficulty,
          }
        );

        // Esegui la mossa
        const moveResult = makeMove(currentState, fastMove.card);
        if (!moveResult.success) {
          addLog(`❌ Errore esecuzione mossa: ${moveResult.error}`);
          break;
        }

        currentState = moveResult.newState;
        moveCount++;

        // Aggiorna progresso
        const progress = Math.min((moveCount / 40) * 100, 100);
        updateStats({ currentGameProgress: progress });

        // Pausa tra le mosse per non sovraccaricare
        if (config.gameSpeed > 0) {
          await new Promise((resolve) => setTimeout(resolve, config.gameSpeed));
        }

        // Controlla se il training è stato fermato
        if (!isRunning || isPaused) {
          addLog("⏸️ Training interrotto dall'utente");
          return false;
        }
      }

      const gameDuration = Date.now() - gameStartTime;
      addLog(
        `✅ Partita completata: ${moveCount} mosse in ${Math.round(
          gameDuration / 1000
        )}s`
      );

      // Genera dati di training
      const trainingData = gameDataCollector.generateTrainingData(currentState);

      if (trainingData && config.autoUpload) {
        try {
          const uploadResult = await collectGameData(trainingData);
          if (uploadResult.success) {
            updateStats({
              successfulUploads: stats.successfulUploads + 1,
            });
            addLog(`📤 Dati caricati: ${uploadResult.fileId}`);
          } else {
            updateStats({
              failedUploads: stats.failedUploads + 1,
            });
            addLog(`❌ Errore upload: ${uploadResult.error}`);
          }
        } catch (error) {
          updateStats({
            failedUploads: stats.failedUploads + 1,
          });
          addLog(`❌ Errore upload: ${error}`);
        }
      }

      // Aggiorna statistiche
      updateStats({
        gamesCompleted: stats.gamesCompleted + 1,
        totalMoves: stats.totalMoves + moveCount,
        averageGameDuration:
          (stats.averageGameDuration * stats.gamesCompleted + gameDuration) /
          (stats.gamesCompleted + 1),
        currentGameProgress: 0,
      });

      // Reset collector per prossima partita
      gameDataCollector.reset();
      setCurrentGame(null);

      return true;
    } catch (error) {
      addLog(`❌ Errore durante partita automatica: ${error}`);
      gameDataCollector.reset();
      setCurrentGame(null);
      return false;
    }
  };

  const startAutoTraining = async () => {
    if (isRunning) return;

    setIsRunning(true);
    setIsPaused(false);
    startTimeRef.current = Date.now();

    addLog(
      `🚀 Avvio auto-training: ${config.targetGames} partite, difficoltà ${config.difficulty}`
    );

    updateStats({
      isRunning: true,
      gamesPlayed: 0,
      gamesCompleted: 0,
    });

    // Loop principale di training
    let gamesPlayed = 0;
    while (gamesPlayed < config.targetGames && isRunning) {
      if (isPaused) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        continue;
      }

      updateStats({ gamesPlayed: gamesPlayed + 1 });

      const success = await playAutomaticGame();
      if (success) {
        gamesPlayed++;

        // Calcola tempo rimanente stimato
        const elapsed = Date.now() - startTimeRef.current;
        const avgTimePerGame = elapsed / gamesPlayed;
        const remaining = (config.targetGames - gamesPlayed) * avgTimePerGame;

        updateStats({
          estimatedTimeRemaining: remaining,
        });
      }

      // Piccola pausa tra le partite
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // Training completato
    setIsRunning(false);
    updateStats({ isRunning: false });
    addLog(`🎉 Auto-training completato! ${gamesPlayed} partite giocate`);
  };

  const pauseTraining = () => {
    setIsPaused(!isPaused);
    addLog(isPaused ? "▶️ Training ripreso" : "⏸️ Training in pausa");
  };

  const stopTraining = () => {
    setIsRunning(false);
    setIsPaused(false);
    updateStats({ isRunning: false });
    addLog("⏹️ Training fermato dall'utente");
  };

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Controlli Principali */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            🤖 Auto-Training CPU vs CPU
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Zap className="h-4 w-4" />
            <AlertDescription>
              <strong>Sistema di Auto-Training:</strong> 4 CPU giocano
              automaticamente in modalità esperto per raccogliere dati di
              training di alta qualità.
            </AlertDescription>
          </Alert>

          {/* Configurazione */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium">Partite Target</label>
              <input
                type="number"
                value={config.targetGames}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    targetGames: parseInt(e.target.value) || 100,
                  }))
                }
                disabled={isRunning}
                className="w-full mt-1 px-3 py-2 border rounded-md"
                min="1"
                max="1000"
              />
            </div>

            <div>
              <label className="text-sm font-medium">Velocità (ms)</label>
              <input
                type="number"
                value={config.gameSpeed}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    gameSpeed: parseInt(e.target.value) || 500,
                  }))
                }
                disabled={isRunning}
                className="w-full mt-1 px-3 py-2 border rounded-md"
                min="0"
                max="2000"
                step="100"
              />
            </div>

            <div>
              <label className="text-sm font-medium">Difficoltà</label>
              <select
                value={config.difficulty}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    difficulty: e.target.value as any,
                  }))
                }
                disabled={isRunning}
                className="w-full mt-1 px-3 py-2 border rounded-md"
              >
                <option value="easy">Principiante</option>
                <option value="medium">Esperto</option>
                <option value="hard">Maestro</option>
              </select>
            </div>

            <div className="flex items-end">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.autoUpload}
                  onChange={(e) =>
                    setConfig((prev) => ({
                      ...prev,
                      autoUpload: e.target.checked,
                    }))
                  }
                  disabled={isRunning}
                />
                <span className="text-sm">Auto Upload</span>
              </label>
            </div>
          </div>

          {/* Preset Configurazioni */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setConfig((prev) => ({
                  ...prev,
                  targetGames: 50,
                  gameSpeed: 100,
                  difficulty: "hard",
                }))
              }
              disabled={isRunning}
            >
              🚀 Veloce (50 partite, 100ms)
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setConfig((prev) => ({
                  ...prev,
                  targetGames: 200,
                  gameSpeed: 0,
                  difficulty: "hard",
                }))
              }
              disabled={isRunning}
            >
              ⚡ Ultra-Veloce (200 partite, 0ms)
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setConfig((prev) => ({
                  ...prev,
                  targetGames: 500,
                  gameSpeed: 0,
                  difficulty: "hard",
                }))
              }
              disabled={isRunning}
            >
              🎯 Dataset Completo (500 partite)
            </Button>
          </div>

          {/* Controlli */}
          <div className="flex gap-2">
            {!isRunning ? (
              <Button
                onClick={startAutoTraining}
                className="flex items-center gap-2"
              >
                <Play className="w-4 h-4" />
                Avvia Auto-Training
              </Button>
            ) : (
              <>
                <Button
                  onClick={pauseTraining}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  {isPaused ? (
                    <Play className="w-4 h-4" />
                  ) : (
                    <Pause className="w-4 h-4" />
                  )}
                  {isPaused ? "Riprendi" : "Pausa"}
                </Button>
                <Button
                  onClick={stopTraining}
                  variant="destructive"
                  className="flex items-center gap-2"
                >
                  <Square className="w-4 h-4" />
                  Ferma
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Statistiche in Tempo Reale */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Partite Completate</p>
                <p className="text-2xl font-bold">
                  {stats.gamesCompleted}/{config.targetGames}
                </p>
              </div>
              <Trophy className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Upload Riusciti</p>
                <p className="text-2xl font-bold text-green-600">
                  {stats.successfulUploads}
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Mosse Totali</p>
                <p className="text-2xl font-bold">
                  {stats.totalMoves.toLocaleString()}
                </p>
              </div>
              <Zap className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Tempo Rimanente</p>
                <p className="text-2xl font-bold">
                  {formatTime(stats.estimatedTimeRemaining)}
                </p>
              </div>
              <Clock className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progresso Partita Corrente */}
      {isRunning && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              Progresso Partita Corrente
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progresso</span>
                <span>{Math.round(stats.currentGameProgress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${stats.currentGameProgress}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>Status: {isPaused ? "In Pausa" : "In Corso"}</span>
                <span>Velocità: {config.gameSpeed}ms/mossa</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Log Attività */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Log Attività
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-500 text-sm">
                Nessuna attività ancora...
              </p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-sm font-mono text-gray-700">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
