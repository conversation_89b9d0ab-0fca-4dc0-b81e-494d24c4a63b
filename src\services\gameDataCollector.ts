/**
 * 🎯 GAME DATA COLLECTOR
 * Raccoglie dati durante la partita per il sistema Community AI
 */

import { GameState } from "@/utils/game/gameLogic";
import { Card, Suit } from "@/utils/game/cardUtils";
import { 
  GameTrainingData, 
  GameMove, 
  TeamData 
} from "@/types/communityAI";

/**
 * Classe per raccogliere dati durante una partita
 */
export class GameDataCollector {
  private moves: GameMove[] = [];
  private gameStartTime: number = 0;
  private isCollecting: boolean = false;
  private gameState: GameState | null = null;

  /**
   * Inizia la raccolta dati per una nuova partita
   */
  public startCollection(initialGameState: GameState): void {
    console.log("🎯 GameDataCollector: Inizio raccolta dati partita");
    
    this.moves = [];
    this.gameStartTime = Date.now();
    this.isCollecting = true;
    this.gameState = initialGameState;
  }

  /**
   * Ferma la raccolta dati
   */
  public stopCollection(): void {
    console.log("🎯 GameDataCollector: Fine raccolta dati partita");
    this.isCollecting = false;
  }

  /**
   * Verifica se la raccolta è attiva
   */
  public isActive(): boolean {
    return this.isCollecting;
  }

  /**
   * Registra una mossa del giocatore
   */
  public recordMove(
    gameState: GameState,
    playerIndex: number,
    playedCard: Card,
    handBefore: Card[],
    trickCardsBefore: Card[]
  ): void {
    if (!this.isCollecting) {
      return;
    }

    const move: GameMove = {
      turn: this.moves.length + 1,
      playerIndex,
      playedCard,
      handBefore: [...handBefore], // Copia per evitare riferimenti
      trickCardsBefore: [...trickCardsBefore],
      trumpSuit: gameState.trumpSuit,
      leadSuit: gameState.leadSuit,
      isAI: this.isPlayerAI(playerIndex),
      aiDifficulty: this.getPlayerAIDifficulty(playerIndex)
    };

    this.moves.push(move);

    console.log(`🎯 GameDataCollector: Registrata mossa ${move.turn} - Giocatore ${playerIndex} gioca ${playedCard.rank} di ${playedCard.suit}`);
  }

  /**
   * Aggiorna l'ultima mossa con informazioni sul vincitore del trick
   */
  public updateLastMoveWithTrickResult(trickWinner: number, trickPoints: number): void {
    if (!this.isCollecting || this.moves.length === 0) {
      return;
    }

    const lastMove = this.moves[this.moves.length - 1];
    lastMove.trickWinner = trickWinner;
    lastMove.trickPoints = trickPoints;

    console.log(`🎯 GameDataCollector: Aggiornata mossa ${lastMove.turn} - Vincitore trick: ${trickWinner}, Punti: ${trickPoints}`);
  }

  /**
   * Verifica se un giocatore è AI
   */
  private isPlayerAI(playerIndex: number): boolean {
    // Il giocatore 0 è sempre umano, gli altri sono AI
    return playerIndex !== 0;
  }

  /**
   * Ottiene la difficoltà AI di un giocatore
   */
  private getPlayerAIDifficulty(playerIndex: number): "easy" | "medium" | "hard" | undefined {
    if (!this.isPlayerAI(playerIndex) || !this.gameState) {
      return undefined;
    }

    // Assumiamo che tutti gli AI abbiano la stessa difficoltà
    // In futuro potrebbe essere personalizzabile per giocatore
    return this.gameState.difficulty || "medium";
  }

  /**
   * Crea i dati del team
   */
  private createTeamData(teamIndex: 0 | 1): TeamData {
    if (!this.gameState) {
      throw new Error("GameState non disponibile");
    }

    // Team 0: giocatori 0 e 2, Team 1: giocatori 1 e 3
    const playerIndices: [number, number] = teamIndex === 0 ? [0, 2] : [1, 3];
    
    const playerNames: [string, string] = [
      this.gameState.players[playerIndices[0]]?.name || `Player ${playerIndices[0]}`,
      this.gameState.players[playerIndices[1]]?.name || `Player ${playerIndices[1]}`
    ];

    const isAI: [boolean, boolean] = [
      this.isPlayerAI(playerIndices[0]),
      this.isPlayerAI(playerIndices[1])
    ];

    const aiDifficulties: [("easy" | "medium" | "hard" | null), ("easy" | "medium" | "hard" | null)] = [
      this.getPlayerAIDifficulty(playerIndices[0]) || null,
      this.getPlayerAIDifficulty(playerIndices[1]) || null
    ];

    return {
      playerIndices,
      playerNames,
      isAI,
      aiDifficulties
    };
  }

  /**
   * Calcola la difficoltà media degli AI
   */
  private calculateAverageAIDifficulty(): "easy" | "medium" | "hard" {
    const difficulties = this.moves
      .filter(move => move.isAI && move.aiDifficulty)
      .map(move => move.aiDifficulty!);

    if (difficulties.length === 0) {
      return "medium"; // Default
    }

    // Conta le occorrenze
    const counts = {
      easy: difficulties.filter(d => d === "easy").length,
      medium: difficulties.filter(d => d === "medium").length,
      hard: difficulties.filter(d => d === "hard").length
    };

    // Ritorna la difficoltà più comune
    if (counts.hard >= counts.medium && counts.hard >= counts.easy) {
      return "hard";
    } else if (counts.medium >= counts.easy) {
      return "medium";
    } else {
      return "easy";
    }
  }

  /**
   * Genera i dati completi della partita per il training
   */
  public generateTrainingData(finalGameState: GameState): GameTrainingData | null {
    if (!this.isCollecting || !this.gameState) {
      console.warn("⚠️ GameDataCollector: Raccolta non attiva o GameState mancante");
      return null;
    }

    try {
      const duration = Date.now() - this.gameStartTime;
      const winnerTeam = finalGameState.gameScore[0] > finalGameState.gameScore[1] ? 0 : 1;
      const scoreDifference = Math.abs(finalGameState.gameScore[0] - finalGameState.gameScore[1]);

      const trainingData: GameTrainingData = {
        version: "1.0",
        timestamp: this.gameStartTime,
        duration,
        teams: {
          team0: this.createTeamData(0),
          team1: this.createTeamData(1)
        },
        moves: [...this.moves], // Copia per sicurezza
        finalScore: {
          team0: finalGameState.gameScore[0],
          team1: finalGameState.gameScore[1]
        },
        winnerTeam,
        victoryPoints: finalGameState.victoryPoints || 31,
        maraffeMade: finalGameState.maraffeMade || [0, 0],
        isDominantWin: scoreDifference > 10,
        metadata: {
          humanPlayerWon: winnerTeam === 0,
          averageAIDifficulty: this.calculateAverageAIDifficulty(),
          totalTricks: Math.floor(this.moves.length / 4) // 4 mosse per trick
        }
      };

      console.log("🎯 GameDataCollector: Dati training generati", {
        moves: trainingData.moves.length,
        duration: `${Math.round(duration / 1000)}s`,
        winner: `Team ${winnerTeam}`,
        score: `${trainingData.finalScore.team0}-${trainingData.finalScore.team1}`
      });

      return trainingData;

    } catch (error) {
      console.error("❌ GameDataCollector: Errore generazione dati training:", error);
      return null;
    }
  }

  /**
   * Reset del collector per una nuova partita
   */
  public reset(): void {
    this.moves = [];
    this.gameStartTime = 0;
    this.isCollecting = false;
    this.gameState = null;
    console.log("🎯 GameDataCollector: Reset completato");
  }

  /**
   * Ottieni statistiche della raccolta corrente
   */
  public getCollectionStats(): {
    isActive: boolean;
    movesRecorded: number;
    elapsedTime: number;
    estimatedTricks: number;
  } {
    return {
      isActive: this.isCollecting,
      movesRecorded: this.moves.length,
      elapsedTime: this.isCollecting ? Date.now() - this.gameStartTime : 0,
      estimatedTricks: Math.floor(this.moves.length / 4)
    };
  }
}

/**
 * Istanza singleton del collector
 */
export const gameDataCollector = new GameDataCollector();
