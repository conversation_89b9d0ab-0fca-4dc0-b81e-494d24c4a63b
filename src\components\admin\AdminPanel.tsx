/**
 * 🧠 ADMIN PANEL COMPONENT
 * Pannello principale di amministrazione per Community AI
 */

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Brain,
  Database,
  BarChart3,
  Settings,
  LogOut,
  RefreshCw,
  Download,
  Upload,
  Trash2,
  AlertCircle,
  CheckCircle,
  Activity,
  Users,
  Clock,
  HardDrive
} from "lucide-react";

import { useAdminAuth } from "@/services/adminAuthService";
import { getCommunityAIStats } from "@/services/communityAIService";
import { CommunityAIDebug } from "@/components/debug/CommunityAIDebug";
import type { CommunityAIStats } from "@/types/communityAI";

interface AdminPanelProps {
  onLogout: () => void;
}

export const AdminPanel: React.FC<AdminPanelProps> = ({ onLogout }) => {
  const { session, hasPermission, extendSession } = useAdminAuth();
  const [stats, setStats] = useState<CommunityAIStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Carica statistiche iniziali
  useEffect(() => {
    loadStats();
    
    // Auto-refresh ogni 30 secondi
    const interval = setInterval(loadStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadStats = () => {
    setStats(getCommunityAIStats());
    setLastUpdate(new Date());
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simula caricamento
    loadStats();
    setIsLoading(false);
  };

  const formatTimeRemaining = (ms: number): string => {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const getSuccessRate = (): number => {
    if (!stats || stats.totalGamesCollected === 0) return 0;
    return Math.round((stats.successfulUploads / stats.totalGamesCollected) * 100);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Brain className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              🧠 Community AI Admin
            </h1>
            <p className="text-gray-600">
              Pannello di controllo per l'addestramento AI
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Info sessione */}
          <div className="text-right text-sm text-gray-600">
            <div>Admin: {session?.email}</div>
            <div>
              Scade tra: {session ? formatTimeRemaining(
                Math.max(0, session.expiresAt - Date.now())
              ) : "N/A"}
            </div>
          </div>

          <Button
            onClick={extendSession}
            variant="outline"
            size="sm"
          >
            <Clock className="w-4 h-4 mr-2" />
            Estendi Sessione
          </Button>

          <Button
            onClick={handleRefresh}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Aggiorna
          </Button>

          <Button
            onClick={onLogout}
            variant="destructive"
            size="sm"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Logout
          </Button>
        </div>
      </div>

      {/* Statistiche rapide */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Partite Raccolte</p>
                <p className="text-3xl font-bold text-gray-900">
                  {stats?.totalGamesCollected || 0}
                </p>
              </div>
              <Database className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Upload Riusciti</p>
                <p className="text-3xl font-bold text-green-600">
                  {stats?.successfulUploads || 0}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tasso Successo</p>
                <p className="text-3xl font-bold text-blue-600">
                  {getSuccessRate()}%
                </p>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Dati Totali</p>
                <p className="text-3xl font-bold text-purple-600">
                  {stats?.totalDataUploaded || 0} KB
                </p>
              </div>
              <HardDrive className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs principali */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">
            <BarChart3 className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="training">
            <Brain className="w-4 h-4 mr-2" />
            Training
          </TabsTrigger>
          <TabsTrigger value="data">
            <Database className="w-4 h-4 mr-2" />
            Dati
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="w-4 h-4 mr-2" />
            Impostazioni
          </TabsTrigger>
          <TabsTrigger value="debug">
            <AlertCircle className="w-4 h-4 mr-2" />
            Debug
          </TabsTrigger>
        </TabsList>

        {/* Tab Overview */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Stato Sistema</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Raccolta Dati</span>
                  <Badge variant="default">Attiva</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Storage Supabase</span>
                  <Badge variant="default">Connesso</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>AI Training</span>
                  <Badge variant="secondary">In Preparazione</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Ultimo Aggiornamento</span>
                  <span className="text-sm text-gray-600">
                    {lastUpdate.toLocaleTimeString()}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Statistiche Recenti</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {stats?.lastSuccessfulUpload ? (
                  <div className="flex items-center justify-between">
                    <span>Ultimo Upload</span>
                    <span className="text-sm text-gray-600">
                      {new Date(stats.lastSuccessfulUpload).toLocaleString()}
                    </span>
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-4">
                    Nessun upload registrato
                  </div>
                )}
                
                {stats?.uploadErrors > 0 && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      {stats.uploadErrors} errori di upload registrati
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Tab Training */}
        <TabsContent value="training" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Stato Addestramento AI</CardTitle>
            </CardHeader>
            <CardContent>
              <Alert>
                <Brain className="h-4 w-4" />
                <AlertDescription>
                  L'addestramento dell'AI non è ancora iniziato. 
                  Sono necessarie almeno 100 partite per iniziare il training.
                </AlertDescription>
              </Alert>
              
              <div className="mt-6 space-y-4">
                <div className="flex items-center justify-between">
                  <span>Partite Necessarie</span>
                  <Badge variant="outline">100</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Partite Raccolte</span>
                  <Badge variant={stats && stats.totalGamesCollected >= 100 ? "default" : "secondary"}>
                    {stats?.totalGamesCollected || 0}
                  </Badge>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ 
                      width: `${Math.min(100, ((stats?.totalGamesCollected || 0) / 100) * 100)}%` 
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab Dati */}
        <TabsContent value="data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Gestione Dati</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-4">
                <Button 
                  disabled={!hasPermission("export")}
                  variant="outline"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Esporta Dati
                </Button>
                <Button 
                  disabled={!hasPermission("write")}
                  variant="outline"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Importa Backup
                </Button>
                <Button 
                  disabled={!hasPermission("delete")}
                  variant="destructive"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Pulisci Dati Vecchi
                </Button>
              </div>
              
              {!hasPermission("write") && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Alcune funzioni richiedono permessi di scrittura.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab Impostazioni */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Configurazione Sistema</CardTitle>
            </CardHeader>
            <CardContent>
              <Alert>
                <Settings className="h-4 w-4" />
                <AlertDescription>
                  Le impostazioni di configurazione saranno disponibili nella prossima versione.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab Debug */}
        <TabsContent value="debug" className="space-y-6">
          <CommunityAIDebug />
        </TabsContent>
      </Tabs>
    </div>
  );
};
