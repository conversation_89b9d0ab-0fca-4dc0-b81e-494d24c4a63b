/**
 * 🧠 ADMIN PANEL COMPONENT
 * Pannello principale di amministrazione per Community AI
 */

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Brain,
  Database,
  BarChart3,
  Settings,
  LogOut,
  RefreshCw,
  Download,
  Upload,
  Trash2,
  AlertCircle,
  CheckCircle,
  Activity,
  Clock,
  HardDrive,
  BookOpen,
  Zap,
} from "lucide-react";

import { useAdminAuth } from "@/services/adminAuthService";
import { getCommunityAIStats } from "@/services/communityAIService";
import { CommunityAIDebug } from "@/components/debug/CommunityAIDebug";
import { TrainingDashboard } from "@/components/admin/TrainingDashboard";
import { TrainedAISettings } from "@/components/admin/TrainedAISettings";
import { AdminNotifications } from "@/components/admin/AdminNotifications";
import { AdminGuide } from "@/components/admin/AdminGuide";
import { AutoTrainingSystem } from "@/components/admin/AutoTrainingSystem";
import { dataManagement } from "@/services/dataManagementService";
import type { CommunityAIStats } from "@/types/communityAI";

interface AdminPanelProps {
  onLogout: () => void;
}

export const AdminPanel: React.FC<AdminPanelProps> = ({ onLogout }) => {
  const { session, hasPermission, extendSession } = useAdminAuth();
  const [stats, setStats] = useState<CommunityAIStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [showGuide, setShowGuide] = useState(false);

  // Carica statistiche iniziali
  useEffect(() => {
    loadStats();

    // Auto-refresh ogni 30 secondi
    const interval = setInterval(loadStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadStats = () => {
    setStats(getCommunityAIStats());
    setLastUpdate(new Date());
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    await new Promise((resolve) => setTimeout(resolve, 1000)); // Simula caricamento
    loadStats();
    setIsLoading(false);
  };

  const formatTimeRemaining = (ms: number): string => {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const getSuccessRate = (): number => {
    if (!stats || stats.totalGamesCollected === 0) return 0;
    return Math.round(
      (stats.successfulUploads / stats.totalGamesCollected) * 100
    );
  };

  const handleExportData = async () => {
    setIsLoading(true);
    try {
      const result = await dataManagement.exportData({
        includeRawData: false,
        includeAnalytics: true,
        includeStats: true,
        format: "json",
      });

      if (result.success && result.data && result.filename) {
        // Download del file
        const blob = new Blob([result.data], { type: "application/json" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = result.filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log("✅ Export completato:", result.filename);
      } else {
        console.error("❌ Errore export:", result.error);
      }
    } catch (error) {
      console.error("❌ Errore export:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateBackup = async () => {
    setIsLoading(true);
    try {
      const result = await dataManagement.createBackup();
      if (result.success) {
        console.log("✅ Backup creato:", result.backupId);
      } else {
        console.error("❌ Errore backup:", result.error);
      }
    } catch (error) {
      console.error("❌ Errore backup:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCleanupData = async () => {
    if (
      !confirm(
        "Sei sicuro di voler eliminare i dati vecchi? Questa azione non può essere annullata."
      )
    ) {
      return;
    }

    setIsLoading(true);
    try {
      const result = await dataManagement.cleanupOldData({
        olderThanDays: 180, // 6 mesi
        keepMinimumFiles: 100,
        dryRun: false,
      });

      if (result.success) {
        console.log("✅ Pulizia completata:", {
          filesDeleted: result.filesDeleted,
          spaceFreed: `${Math.round(result.spaceFreed / 1024)}KB`,
        });
        loadStats(); // Ricarica statistiche
      } else {
        console.error("❌ Errore pulizia:", result.error);
      }
    } catch (error) {
      console.error("❌ Errore pulizia:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Brain className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              🧠 Community AI Admin
            </h1>
            <p className="text-gray-600">
              Pannello di controllo per l'addestramento AI
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Info sessione */}
          <div className="text-right text-sm text-gray-600">
            <div>Admin: {session?.email}</div>
            <div>
              Scade tra:{" "}
              {session
                ? formatTimeRemaining(
                    Math.max(0, session.expiresAt - Date.now())
                  )
                : "N/A"}
            </div>
          </div>

          {/* Notifiche Admin */}
          <AdminNotifications />

          <Button
            onClick={() => setShowGuide(true)}
            variant="outline"
            size="sm"
          >
            <BookOpen className="w-4 h-4 mr-2" />
            Guida
          </Button>

          <Button onClick={extendSession} variant="outline" size="sm">
            <Clock className="w-4 h-4 mr-2" />
            Estendi Sessione
          </Button>

          <Button
            onClick={handleRefresh}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
            />
            Aggiorna
          </Button>

          <Button onClick={onLogout} variant="destructive" size="sm">
            <LogOut className="w-4 h-4 mr-2" />
            Logout
          </Button>
        </div>
      </div>

      {/* Statistiche rapide */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Partite Raccolte
                </p>
                <p className="text-3xl font-bold text-gray-900">
                  {stats?.totalGamesCollected || 0}
                </p>
              </div>
              <Database className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Upload Riusciti
                </p>
                <p className="text-3xl font-bold text-green-600">
                  {stats?.successfulUploads || 0}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Tasso Successo
                </p>
                <p className="text-3xl font-bold text-blue-600">
                  {getSuccessRate()}%
                </p>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Dati Totali</p>
                <p className="text-3xl font-bold text-purple-600">
                  {stats?.totalDataUploaded || 0} KB
                </p>
              </div>
              <HardDrive className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs principali */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">
            <BarChart3 className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="training">
            <Brain className="w-4 h-4 mr-2" />
            Training
          </TabsTrigger>
          <TabsTrigger value="auto-training">
            <Zap className="w-4 h-4 mr-2" />
            Auto-Training
          </TabsTrigger>
          <TabsTrigger value="data">
            <Database className="w-4 h-4 mr-2" />
            Dati
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="w-4 h-4 mr-2" />
            Impostazioni
          </TabsTrigger>
          <TabsTrigger value="debug">
            <AlertCircle className="w-4 h-4 mr-2" />
            Debug
          </TabsTrigger>
        </TabsList>

        {/* Tab Overview */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Stato Sistema</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Raccolta Dati</span>
                  <Badge variant="default">Attiva</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Storage Supabase</span>
                  <Badge variant="default">Connesso</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>AI Training</span>
                  <Badge variant="secondary">In Preparazione</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Ultimo Aggiornamento</span>
                  <span className="text-sm text-gray-600">
                    {lastUpdate.toLocaleTimeString()}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Statistiche Recenti</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {stats?.lastSuccessfulUpload ? (
                  <div className="flex items-center justify-between">
                    <span>Ultimo Upload</span>
                    <span className="text-sm text-gray-600">
                      {new Date(stats.lastSuccessfulUpload).toLocaleString()}
                    </span>
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-4">
                    Nessun upload registrato
                  </div>
                )}

                {stats?.uploadErrors > 0 && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      {stats.uploadErrors} errori di upload registrati
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Tab Training */}
        <TabsContent value="training" className="space-y-6">
          <TrainingDashboard />
        </TabsContent>

        {/* Tab Auto-Training */}
        <TabsContent value="auto-training" className="space-y-6">
          <AutoTrainingSystem />
        </TabsContent>

        {/* Tab Dati */}
        <TabsContent value="data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Gestione Dati</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-4">
                <Button
                  onClick={handleExportData}
                  disabled={!hasPermission("export") || isLoading}
                  variant="outline"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Esporta Dati
                </Button>
                <Button
                  onClick={handleCreateBackup}
                  disabled={!hasPermission("write") || isLoading}
                  variant="outline"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Crea Backup
                </Button>
                <Button
                  onClick={handleCleanupData}
                  disabled={!hasPermission("delete") || isLoading}
                  variant="destructive"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Pulisci Dati Vecchi
                </Button>
              </div>

              {!hasPermission("write") && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Alcune funzioni richiedono permessi di scrittura.
                  </AlertDescription>
                </Alert>
              )}

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Export Dati:</strong> Scarica statistiche e analytics
                  in formato JSON.
                  <br />
                  <strong>Crea Backup:</strong> Crea una copia di sicurezza di
                  tutti i dati.
                  <br />
                  <strong>Pulisci Dati:</strong> Elimina file più vecchi di 6
                  mesi (mantiene minimo 100 file).
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab Impostazioni */}
        <TabsContent value="settings" className="space-y-6">
          <TrainedAISettings />
        </TabsContent>

        {/* Tab Debug */}
        <TabsContent value="debug" className="space-y-6">
          <CommunityAIDebug />
        </TabsContent>
      </Tabs>

      {/* Guida Admin */}
      <AdminGuide isOpen={showGuide} onClose={() => setShowGuide(false)} />
    </div>
  );
};
