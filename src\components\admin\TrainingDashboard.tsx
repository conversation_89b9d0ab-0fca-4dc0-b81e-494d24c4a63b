/**
 * 📊 TRAINING DASHBOARD COMPONENT
 * Dashboard per visualizzare le statistiche di training dell'AI
 */

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Brain,
  TrendingUp,
  Target,
  Clock,
  Database,
  RefreshCw,
  Download,
  AlertCircle,
  CheckCircle,
  BarChart3,
  PieChart,
  Activity,
} from "lucide-react";

import {
  trainingAnalytics,
  type TrainingMetrics,
  type TrainingProgress,
} from "@/services/trainingAnalyticsService";
import { AdminStatsCard } from "@/components/admin/AdminStatsCard";
import {
  SimpleBarChart,
  SimpleProgressRing,
} from "@/components/admin/SimpleChart";

export const TrainingDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<TrainingMetrics | null>(null);
  const [progress, setProgress] = useState<TrainingProgress | null>(null);
  const [patterns, setPatterns] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [metricsData, progressData, patternsData] = await Promise.all([
        trainingAnalytics.getTrainingMetrics(),
        trainingAnalytics.getTrainingProgress(),
        trainingAnalytics.analyzeGamePatterns(),
      ]);

      setMetrics(metricsData);
      setProgress(progressData);
      setPatterns(patternsData);
      setLastUpdate(new Date());
    } catch (error) {
      console.error("❌ Errore caricamento dati training:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    trainingAnalytics.clearCache();
    await loadData();
  };

  const handleExport = async () => {
    try {
      const exportData = await trainingAnalytics.exportMetrics();
      const blob = new Blob([exportData], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `training-metrics-${
        new Date().toISOString().split("T")[0]
      }.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("❌ Errore export:", error);
    }
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case "collecting":
        return "bg-yellow-500";
      case "preprocessing":
        return "bg-blue-500";
      case "training":
        return "bg-purple-500";
      case "validation":
        return "bg-orange-500";
      case "ready":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const getPhaseLabel = (phase: string) => {
    switch (phase) {
      case "collecting":
        return "Raccolta Dati";
      case "preprocessing":
        return "Preprocessing";
      case "training":
        return "Training";
      case "validation":
        return "Validazione";
      case "ready":
        return "Pronto";
      default:
        return "Sconosciuto";
    }
  };

  if (!metrics || !progress) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">Caricamento statistiche training...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Brain className="w-8 h-8 text-blue-600" />
          <div>
            <h2 className="text-2xl font-bold">Training Dashboard</h2>
            <p className="text-gray-600">
              Ultimo aggiornamento: {lastUpdate.toLocaleTimeString()}
            </p>
          </div>
        </div>

        <div className="flex space-x-2">
          <Button onClick={handleExport} variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Esporta
          </Button>
          <Button
            onClick={handleRefresh}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
            />
            Aggiorna
          </Button>
        </div>
      </div>

      {/* Stato Training */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Stato Training
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div
                className={`w-3 h-3 rounded-full ${getPhaseColor(
                  progress.phase
                )}`}
              />
              <span className="font-medium">
                {getPhaseLabel(progress.phase)}
              </span>
            </div>
            <Badge variant="outline">
              {progress.progress.toFixed(1)}% completato
            </Badge>
          </div>

          <Progress value={progress.progress} className="w-full" />

          {progress.recommendations.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Raccomandazioni:</h4>
              {progress.recommendations.map((rec, index) => (
                <Alert key={index}>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-sm">{rec}</AlertDescription>
                </Alert>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Metriche Principali */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <AdminStatsCard
          title="Partite Totali"
          value={metrics.totalGames}
          icon={Database}
          iconColor="text-blue-500"
          status={metrics.totalGames >= 100 ? "success" : "info"}
        />

        <AdminStatsCard
          title="Mosse Totali"
          value={metrics.totalMoves.toLocaleString()}
          icon={Target}
          iconColor="text-green-500"
        />

        <AdminStatsCard
          title="Durata Media"
          value={`${Math.round(metrics.averageGameDuration / 60000)}m`}
          icon={Clock}
          iconColor="text-purple-500"
        />

        <AdminStatsCard
          title="Qualità Dati"
          value={`${metrics.dataQuality.qualityScore}%`}
          icon={CheckCircle}
          iconColor="text-orange-500"
          status={
            metrics.dataQuality.qualityScore >= 80
              ? "success"
              : metrics.dataQuality.qualityScore >= 60
              ? "warning"
              : "error"
          }
        />
      </div>

      {/* Win Rate per Difficoltà */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Win Rate per Difficoltà
          </CardTitle>
        </CardHeader>
        <CardContent>
          <SimpleBarChart
            data={Object.entries(metrics.winRateByDifficulty).map(
              ([difficulty, rate]) => ({
                label: difficulty.charAt(0).toUpperCase() + difficulty.slice(1),
                value: Math.round(rate),
                color:
                  difficulty === "easy"
                    ? "#10b981"
                    : difficulty === "medium"
                    ? "#f59e0b"
                    : "#ef4444",
              })
            )}
            height={200}
            showValues={true}
          />
        </CardContent>
      </Card>

      {/* Carte Più Giocate */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5" />
              Carte Più Giocate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.mostPlayedCards.slice(0, 5).map((card, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline">{index + 1}</Badge>
                    <span className="font-medium">{card.card}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {card.count} volte
                    </div>
                    <div className="text-xs text-gray-600">
                      {card.winRate}% win rate
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Pattern di Gioco */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Pattern di Gioco
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>Win Rate Giocatore</span>
              <Badge variant="default">
                {metrics.playerPatterns.humanPlayerWinRate.toFixed(1)}%
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Mosse per Partita</span>
              <Badge variant="outline">
                {metrics.playerPatterns.averageMovesPerGame.toFixed(1)}
              </Badge>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-sm">Aperture Comuni:</h4>
              {metrics.playerPatterns.commonOpeningMoves
                .slice(0, 3)
                .map((move, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span>{move.move}</span>
                    <span className="text-gray-600">{move.frequency}%</span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Strategie e Errori */}
      {patterns && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-green-600">
                Strategie Vincenti
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {patterns.winningStrategies.map(
                  (strategy: any, index: number) => (
                    <div key={index} className="p-3 bg-green-50 rounded-lg">
                      <div className="font-medium text-sm">
                        {strategy.strategy}
                      </div>
                      <div className="flex justify-between text-xs text-gray-600 mt-1">
                        <span>Win Rate: {strategy.winRate}%</span>
                        <span>Frequenza: {strategy.frequency}%</span>
                      </div>
                    </div>
                  )
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Errori Comuni</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {patterns.commonMistakes.map((mistake: any, index: number) => (
                  <div key={index} className="p-3 bg-red-50 rounded-lg">
                    <div className="font-medium text-sm">{mistake.mistake}</div>
                    <div className="flex justify-between text-xs text-gray-600 mt-1">
                      <span>Frequenza: {mistake.frequency}%</span>
                      <span>Impatto: -{mistake.impact} punti</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
