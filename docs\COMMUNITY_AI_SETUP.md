# 🧠 Community AI - Guida Setup

Questa guida spiega come configurare il sistema Community AI per raccogliere dati delle partite.

## 📋 Prerequisiti

- Progetto Supabase configurato
- Accesso admin al dashboard Supabase
- App configurata con credenziali Supabase

## 🚀 Setup Automatico

Il sistema include un setup automatico che:

1. **Verifica** se il bucket `ai-training-data` esiste
2. **Crea** il bucket se necessario
3. **Testa** l'upload di file
4. **Verifica** le policy di accesso

### Esecuzione Setup

Il setup viene eseguito automaticamente quando:
- Il servizio Community AI viene inizializzato
- Viene chiamato `communityAI.initialize()`

```typescript
import { communityAI } from '@/services/communityAIService';

// Inizializzazione automatica
const result = await communityAI.initialize();
if (result.success) {
  console.log('✅ Community AI pronto');
} else {
  console.error('❌ Errore setup:', result.error);
}
```

## 🔧 Setup Manuale (Opzionale)

Se il setup automatico non funziona, puoi configurare manualmente:

### 1. Crea il Bucket

Nel dashboard Supabase > Storage:

```sql
-- Esegui questo SQL nel dashboard Supabase
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'ai-training-data',
  'ai-training-data',
  false,
  102400, -- 100KB limite
  ARRAY['application/json', 'application/gzip', 'text/plain']
);
```

### 2. Configura le Policy

```sql
-- Policy per upload anonimi
CREATE POLICY "Allow anonymous uploads to ai-training-data"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'ai-training-data' AND
  name ~ '\.json\.gz$'
);

-- Policy per lettura admin (opzionale)
CREATE POLICY "Allow admin read access to ai-training-data"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'ai-training-data' AND
  auth.jwt() ->> 'role' = 'admin'
);
```

### 3. Abilita RLS

```sql
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
```

## 🧪 Test della Configurazione

### Test Automatico

```typescript
import { setupCommunityAIStorage } from '@/utils/supabase/storageSetup';

const result = await setupCommunityAIStorage();
console.log('Setup result:', result);
```

### Test Manuale

```typescript
import { testAITrainingUpload } from '@/utils/supabase/storageSetup';

const uploadTest = await testAITrainingUpload();
if (uploadTest.success) {
  console.log('✅ Upload funziona');
} else {
  console.error('❌ Errore upload:', uploadTest.error);
}
```

### Debug

```typescript
import { debugBucketInfo } from '@/utils/supabase/storageSetup';

await debugBucketInfo(); // Stampa info dettagliate
```

## 📊 Monitoraggio

### Statistiche Locali

```typescript
import { getCommunityAIStats } from '@/services/communityAIService';

const stats = getCommunityAIStats();
console.log('Statistiche:', stats);
```

### Statistiche Supabase (Opzionale)

Se hai creato la tabella `ai_training_stats`:

```sql
SELECT * FROM public.ai_training_stats;
```

## 🔒 Privacy e Sicurezza

### Dati Raccolti

- **Mosse dei giocatori**: Carte giocate, ordine, timing
- **Stato del gioco**: Punteggi, briscola, trick
- **Metadati**: Difficoltà AI, durata partita
- **NON raccolti**: Dati personali, ID utente, informazioni identificative

### Configurazione Privacy

```typescript
// Disabilita raccolta dati
communityAI.updateConfig({ enabled: false });

// Configura limiti
communityAI.updateConfig({
  maxCompressedSize: 50, // 50KB max
  maxRetries: 1,
  uploadTimeout: 5000 // 5 secondi
});
```

## 🚨 Troubleshooting

### Errore "Bucket not found"

1. Verifica che il bucket esista nel dashboard Supabase
2. Esegui il setup automatico: `communityAI.initialize()`
3. Se persiste, crea manualmente il bucket

### Errore "Permission denied"

1. Verifica le policy RLS
2. Controlla che il bucket non sia pubblico
3. Verifica le credenziali Supabase

### Errore "File too large"

1. Controlla il limite del bucket (100KB)
2. Verifica la compressione dei dati
3. Riduci `maxCompressedSize` nella config

### Upload fallisce sempre

1. Verifica connessione internet
2. Controlla credenziali Supabase
3. Testa con `debugBucketInfo()`

## 📈 Metriche di Successo

- **Upload rate**: >90% di successo
- **File size**: <50KB media
- **Response time**: <5 secondi
- **Error rate**: <5%

## 🔄 Manutenzione

### Pulizia Periodica

I file vengono mantenuti indefinitamente per il training. Per pulizia:

```sql
-- Elimina file più vecchi di 6 mesi
DELETE FROM storage.objects 
WHERE bucket_id = 'ai-training-data' 
AND created_at < NOW() - INTERVAL '6 months';
```

### Monitoraggio Spazio

```sql
-- Verifica utilizzo spazio
SELECT 
  COUNT(*) as file_count,
  SUM(metadata->>'size')::bigint as total_size_bytes
FROM storage.objects 
WHERE bucket_id = 'ai-training-data';
```
