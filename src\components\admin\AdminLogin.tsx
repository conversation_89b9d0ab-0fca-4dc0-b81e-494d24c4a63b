/**
 * 🔐 ADMIN LOGIN COMPONENT
 * Componente per l'autenticazione admin
 */

import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Shield, 
  Key, 
  LogIn, 
  AlertCircle,
  Eye,
  EyeOff
} from "lucide-react";

interface AdminLoginProps {
  onLogin: (key?: string) => Promise<{ success: boolean; error?: string }>;
  isLoading?: boolean;
}

export const AdminLogin: React.FC<AdminLoginProps> = ({ 
  onLogin, 
  isLoading = false 
}) => {
  const [tempKey, setTempKey] = useState("");
  const [showKey, setShowKey] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loginMethod, setLoginMethod] = useState<"temp" | "supabase">("temp");

  const handleTempLogin = async () => {
    if (!tempKey.trim()) {
      setError("Inserisci la chiave di accesso");
      return;
    }

    setError(null);
    const result = await onLogin(tempKey);
    
    if (!result.success) {
      setError(result.error || "Login fallito");
    }
  };

  const handleSupabaseLogin = async () => {
    setError(null);
    const result = await onLogin();
    
    if (!result.success) {
      setError(result.error || "Login fallito");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && loginMethod === "temp") {
      handleTempLogin();
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Shield className="w-6 h-6 text-blue-600" />
          </div>
          <CardTitle className="text-2xl font-bold">
            🧠 Admin Panel
          </CardTitle>
          <p className="text-muted-foreground">
            Accesso riservato agli amministratori
          </p>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Selezione metodo login */}
          <div className="flex space-x-2">
            <Button
              variant={loginMethod === "temp" ? "default" : "outline"}
              size="sm"
              onClick={() => setLoginMethod("temp")}
              className="flex-1"
            >
              <Key className="w-4 h-4 mr-2" />
              Chiave Temp
            </Button>
            <Button
              variant={loginMethod === "supabase" ? "default" : "outline"}
              size="sm"
              onClick={() => setLoginMethod("supabase")}
              className="flex-1"
            >
              <LogIn className="w-4 h-4 mr-2" />
              Supabase
            </Button>
          </div>

          {/* Login con chiave temporanea */}
          {loginMethod === "temp" && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="tempKey">Chiave di Accesso</Label>
                <div className="relative">
                  <Input
                    id="tempKey"
                    type={showKey ? "text" : "password"}
                    value={tempKey}
                    onChange={(e) => setTempKey(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Inserisci la chiave admin"
                    disabled={isLoading}
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowKey(!showKey)}
                    disabled={isLoading}
                  >
                    {showKey ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <Button 
                onClick={handleTempLogin} 
                disabled={isLoading || !tempKey.trim()}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Accesso in corso...
                  </>
                ) : (
                  <>
                    <Key className="w-4 h-4 mr-2" />
                    Accedi con Chiave
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Login con Supabase */}
          {loginMethod === "supabase" && (
            <div className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Devi essere autenticato con un account admin autorizzato.
                </AlertDescription>
              </Alert>

              <Button 
                onClick={handleSupabaseLogin} 
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Verifica in corso...
                  </>
                ) : (
                  <>
                    <LogIn className="w-4 h-4 mr-2" />
                    Accedi con Supabase
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Errore */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Info sviluppo */}
          {process.env.NODE_ENV === "development" && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                <strong>Dev Mode:</strong> Chiave temporanea per testing disponibile.
                In produzione utilizzare solo autenticazione Supabase.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
