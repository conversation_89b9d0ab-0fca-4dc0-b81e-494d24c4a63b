/**
 * 🧪 DATA FLOW TEST COMPONENT
 * Componente per testare il flusso completo di raccolta dati
 */

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Database,
  Upload,
  BarChart3,
  RefreshCw
} from "lucide-react";

import { gameDataCollector } from "@/services/gameDataCollector";
import { collectGameData } from "@/services/communityAIService";
import { getCommunityAIStats } from "@/services/communityAIService";
import { trainingAnalytics } from "@/services/trainingAnalyticsService";
import { initializeGameState } from "@/utils/game/gameLogic";
import { Card as GameCard, Suit } from "@/utils/game/cardUtils";

interface TestStep {
  id: string;
  name: string;
  status: "pending" | "running" | "success" | "error";
  message: string;
  details?: any;
}

export const DataFlowTest: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [steps, setSteps] = useState<TestStep[]>([]);
  const [testResults, setTestResults] = useState<any>(null);

  const updateStep = (id: string, updates: Partial<TestStep>) => {
    setSteps(prev => prev.map(step => 
      step.id === id ? { ...step, ...updates } : step
    ));
  };

  const runCompleteDataFlowTest = async () => {
    setIsRunning(true);
    setTestResults(null);
    
    const testSteps: TestStep[] = [
      { id: "init", name: "Inizializzazione Collector", status: "pending", message: "In attesa..." },
      { id: "simulate", name: "Simulazione Partita", status: "pending", message: "In attesa..." },
      { id: "generate", name: "Generazione Dati Training", status: "pending", message: "In attesa..." },
      { id: "upload", name: "Upload a Supabase", status: "pending", message: "In attesa..." },
      { id: "verify", name: "Verifica Dati Ricevuti", status: "pending", message: "In attesa..." },
      { id: "analytics", name: "Aggiornamento Analytics", status: "pending", message: "In attesa..." }
    ];
    
    setSteps(testSteps);

    try {
      // Step 1: Inizializzazione Collector
      updateStep("init", { status: "running", message: "Inizializzando collector..." });
      
      const gameState = initializeGameState(31);
      gameDataCollector.startCollection(gameState);
      
      if (gameDataCollector.isActive()) {
        updateStep("init", { 
          status: "success", 
          message: "Collector inizializzato correttamente",
          details: { isActive: true }
        });
      } else {
        updateStep("init", { 
          status: "error", 
          message: "Collector non si è attivato"
        });
        return;
      }

      // Step 2: Simulazione Partita
      updateStep("simulate", { status: "running", message: "Simulando partita completa..." });
      
      const mockCard: GameCard = { 
        id: "test-card", 
        suit: Suit.Coins, 
        rank: "A", 
        displayName: "Asso di Denari Test" 
      };

      // Simula 40 mosse (partita completa)
      for (let i = 0; i < 40; i++) {
        gameDataCollector.recordMove(
          gameState,
          i % 4, // Giocatore
          mockCard,
          [mockCard], // Hand prima
          [] // Trick cards prima
        );
        
        // Ogni 4 mosse, completa il trick
        if ((i + 1) % 4 === 0) {
          gameDataCollector.updateLastMoveWithTrickResult(0, 1); // Giocatore 0 vince, 1 punto
        }
      }

      updateStep("simulate", { 
        status: "success", 
        message: "Partita simulata: 40 mosse, 10 trick",
        details: { moves: 40, tricks: 10 }
      });

      // Step 3: Generazione Dati Training
      updateStep("generate", { status: "running", message: "Generando dati training..." });
      
      const finalGameState = {
        ...gameState,
        gameScore: [21, 10] as [number, number],
        gamePhase: "gameOver" as const
      };

      const trainingData = gameDataCollector.generateTrainingData(finalGameState);
      
      if (trainingData && trainingData.moves.length > 0) {
        updateStep("generate", { 
          status: "success", 
          message: `Dati generati: ${trainingData.moves.length} mosse`,
          details: { 
            moves: trainingData.moves.length,
            duration: trainingData.duration,
            teams: trainingData.teams
          }
        });
      } else {
        updateStep("generate", { 
          status: "error", 
          message: "Errore generazione dati training"
        });
        return;
      }

      // Step 4: Upload a Supabase
      updateStep("upload", { status: "running", message: "Caricando su Supabase..." });
      
      const uploadResult = await collectGameData(trainingData);
      
      if (uploadResult.success) {
        updateStep("upload", { 
          status: "success", 
          message: `Upload riuscito: ${uploadResult.fileId}`,
          details: { 
            fileId: uploadResult.fileId,
            fileSize: uploadResult.fileSize 
          }
        });
      } else {
        updateStep("upload", { 
          status: "error", 
          message: `Errore upload: ${uploadResult.error}`
        });
        return;
      }

      // Step 5: Verifica Dati Ricevuti
      updateStep("verify", { status: "running", message: "Verificando dati ricevuti..." });
      
      // Aspetta un momento per permettere la propagazione
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const stats = getCommunityAIStats();
      
      if (stats.totalGamesCollected > 0) {
        updateStep("verify", { 
          status: "success", 
          message: `Dati verificati: ${stats.totalGamesCollected} partite totali`,
          details: stats
        });
      } else {
        updateStep("verify", { 
          status: "error", 
          message: "Dati non trovati nelle statistiche"
        });
      }

      // Step 6: Aggiornamento Analytics
      updateStep("analytics", { status: "running", message: "Aggiornando analytics..." });
      
      try {
        // Forza refresh delle metriche
        trainingAnalytics.clearCache();
        const metrics = await trainingAnalytics.getTrainingMetrics(true);
        
        updateStep("analytics", { 
          status: "success", 
          message: `Analytics aggiornate: ${metrics.totalGames} partite analizzate`,
          details: { 
            totalGames: metrics.totalGames,
            totalMoves: metrics.totalMoves,
            qualityScore: metrics.dataQuality.qualityScore
          }
        });

        setTestResults({
          success: true,
          stats,
          metrics,
          trainingData: {
            moves: trainingData.moves.length,
            duration: trainingData.duration,
            teams: trainingData.teams
          }
        });

      } catch (error) {
        updateStep("analytics", { 
          status: "error", 
          message: `Errore analytics: ${error}`
        });
      }

      // Cleanup
      gameDataCollector.reset();

    } catch (error) {
      console.error("❌ Errore test flusso dati:", error);
      
      // Aggiorna step corrente con errore
      const currentStep = steps.find(s => s.status === "running");
      if (currentStep) {
        updateStep(currentStep.id, { 
          status: "error", 
          message: `Errore: ${error}`
        });
      }
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestStep['status']) => {
    switch (status) {
      case "success": return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "error": return <XCircle className="w-4 h-4 text-red-500" />;
      case "running": return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      case "pending": return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TestStep['status']) => {
    switch (status) {
      case "success": return "border-green-200 bg-green-50";
      case "error": return "border-red-200 bg-red-50";
      case "running": return "border-blue-200 bg-blue-50";
      case "pending": return "border-gray-200 bg-gray-50";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Test Flusso Completo Raccolta Dati
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <BarChart3 className="h-4 w-4" />
          <AlertDescription>
            Questo test simula una partita completa e verifica che tutti i componenti del sistema Community AI funzionino correttamente.
          </AlertDescription>
        </Alert>

        <Button 
          onClick={runCompleteDataFlowTest}
          disabled={isRunning}
          className="w-full"
        >
          {isRunning ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Test in corso...
            </>
          ) : (
            <>
              <Play className="w-4 h-4 mr-2" />
              Esegui Test Completo
            </>
          )}
        </Button>

        {steps.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Progresso Test:</h4>
            {steps.map((step) => (
              <div
                key={step.id}
                className={`p-3 rounded-lg border ${getStatusColor(step.status)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(step.status)}
                    <div>
                      <div className="font-medium text-sm">{step.name}</div>
                      <div className="text-sm text-gray-600">{step.message}</div>
                    </div>
                  </div>
                  <Badge variant="outline">
                    {step.status}
                  </Badge>
                </div>
                {step.details && (
                  <div className="mt-2 text-xs text-gray-500">
                    <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                      {JSON.stringify(step.details, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {testResults && (
          <Alert variant={testResults.success ? "default" : "destructive"}>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Test Completato!</strong><br/>
              Il flusso completo di raccolta dati funziona correttamente. 
              Partite raccolte: {testResults.stats.totalGamesCollected}, 
              Mosse analizzate: {testResults.metrics.totalMoves}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};
