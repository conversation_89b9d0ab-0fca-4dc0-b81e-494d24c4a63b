/**
 * 🧠 ADMIN DASHBOARD PAGE
 * Pagina principale per l'amministrazione Community AI
 */

import React, { useState } from "react";
import { useAdminAuth } from "@/services/adminAuthService";
import { AdminLogin } from "@/components/admin/AdminLogin";
import { AdminPanel } from "@/components/admin/AdminPanel";

export const AdminDashboard: React.FC = () => {
  const { isAdmin, login, logout } = useAdminAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async (key?: string) => {
    setIsLoading(true);
    try {
      const result = await login(key);
      return result;
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
  };

  if (!isAdmin) {
    return (
      <AdminLogin 
        onLogin={handleLogin}
        isLoading={isLoading}
      />
    );
  }

  return (
    <AdminPanel onLogout={handleLogout} />
  );
};
