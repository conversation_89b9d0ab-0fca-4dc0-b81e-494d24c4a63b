/**
 * 🧠 COMMUNITY AI - Test di Integrazione
 * Test completo del sistema Community AI
 */

import { GameState, initializeGameState } from "@/utils/game/gameLogic";
import { gameDataCollector } from "@/services/gameDataCollector";
import { collectGameData } from "@/services/communityAIService";
import { Card, Suit } from "@/utils/game/cardUtils";
import type { GameTrainingData } from "@/types/communityAI";

/**
 * Crea dati di test per una partita simulata
 */
export const createMockGameData = (): GameTrainingData => {
  const timestamp = Date.now();
  
  return {
    version: "1.0",
    timestamp,
    duration: 300000, // 5 minuti
    teams: {
      team0: {
        playerIndices: [0, 2],
        playerNames: ["Giocatore", "Compagno AI"],
        isAI: [false, true],
        aiDifficulties: [null, "medium"]
      },
      team1: {
        playerIndices: [1, 3],
        playerNames: ["Avversario 1", "Avversario 2"],
        isAI: [true, true],
        aiDifficulties: ["medium", "medium"]
      }
    },
    moves: [
      {
        turn: 1,
        playerIndex: 0,
        playedCard: { id: "1", suit: Suit.Coins, rank: "A", displayName: "Asso di Denari" },
        handBefore: [
          { id: "1", suit: Suit.Coins, rank: "A", displayName: "Asso di Denari" },
          { id: "2", suit: Suit.Coins, rank: "K", displayName: "Re di Denari" }
        ],
        trickCardsBefore: [],
        trumpSuit: Suit.Coins,
        leadSuit: null,
        isAI: false,
        trickWinner: 0,
        trickPoints: 1
      },
      {
        turn: 2,
        playerIndex: 1,
        playedCard: { id: "3", suit: Suit.Coins, rank: "2", displayName: "2 di Denari" },
        handBefore: [
          { id: "3", suit: Suit.Coins, rank: "2", displayName: "2 di Denari" },
          { id: "4", suit: Suit.Cups, rank: "K", displayName: "Re di Coppe" }
        ],
        trickCardsBefore: [
          { id: "1", suit: Suit.Coins, rank: "A", displayName: "Asso di Denari" }
        ],
        trumpSuit: Suit.Coins,
        leadSuit: Suit.Coins,
        isAI: true,
        aiDifficulty: "medium"
      }
    ],
    finalScore: {
      team0: 21,
      team1: 10
    },
    winnerTeam: 0,
    victoryPoints: 31,
    maraffeMade: [1, 0],
    isDominantWin: true,
    metadata: {
      humanPlayerWon: true,
      averageAIDifficulty: "medium",
      totalTricks: 10
    }
  };
};

/**
 * Test completo del sistema Community AI
 */
export const runCommunityAIIntegrationTest = async (): Promise<{
  success: boolean;
  results: {
    dataGeneration: boolean;
    dataCompression: boolean;
    dataUpload: boolean;
    collectorIntegration: boolean;
  };
  errors: string[];
}> => {
  const results = {
    dataGeneration: false,
    dataCompression: false,
    dataUpload: false,
    collectorIntegration: false
  };
  const errors: string[] = [];

  try {
    console.log("🧪 Iniziando test integrazione Community AI...");

    // Test 1: Generazione dati
    console.log("🧪 Test 1: Generazione dati mock...");
    try {
      const mockData = createMockGameData();
      if (mockData && mockData.moves.length > 0) {
        results.dataGeneration = true;
        console.log("✅ Generazione dati: OK");
      } else {
        errors.push("Dati mock non validi");
      }
    } catch (error) {
      errors.push(`Errore generazione dati: ${error}`);
    }

    // Test 2: Compressione dati
    console.log("🧪 Test 2: Compressione dati...");
    try {
      const mockData = createMockGameData();
      const jsonString = JSON.stringify(mockData);
      const compressedData = btoa(jsonString); // Simulazione compressione
      
      if (compressedData.length > 0 && compressedData.length < jsonString.length * 2) {
        results.dataCompression = true;
        console.log("✅ Compressione dati: OK");
      } else {
        errors.push("Compressione non efficace");
      }
    } catch (error) {
      errors.push(`Errore compressione: ${error}`);
    }

    // Test 3: Upload dati
    console.log("🧪 Test 3: Upload dati...");
    try {
      const mockData = createMockGameData();
      const uploadResult = await collectGameData(mockData);
      
      if (uploadResult.success) {
        results.dataUpload = true;
        console.log("✅ Upload dati: OK");
      } else {
        errors.push(`Errore upload: ${uploadResult.error}`);
      }
    } catch (error) {
      errors.push(`Errore upload: ${error}`);
    }

    // Test 4: Integrazione collector
    console.log("🧪 Test 4: Integrazione collector...");
    try {
      // Simula una partita
      const gameState = initializeGameState(31);
      gameDataCollector.startCollection(gameState);
      
      // Simula alcune mosse
      const mockCard: Card = { 
        id: "test", 
        suit: Suit.Coins, 
        rank: "A", 
        displayName: "Test Card" 
      };
      
      gameDataCollector.recordMove(
        gameState,
        0,
        mockCard,
        [mockCard],
        []
      );
      
      gameDataCollector.updateLastMoveWithTrickResult(0, 1);
      
      // Genera dati training
      const finalGameState = {
        ...gameState,
        gameScore: [21, 10] as [number, number],
        gamePhase: "gameOver" as const
      };
      
      const trainingData = gameDataCollector.generateTrainingData(finalGameState);
      
      if (trainingData && trainingData.moves.length > 0) {
        results.collectorIntegration = true;
        console.log("✅ Integrazione collector: OK");
      } else {
        errors.push("Collector non ha generato dati validi");
      }
      
      // Cleanup
      gameDataCollector.reset();
      
    } catch (error) {
      errors.push(`Errore integrazione collector: ${error}`);
    }

    const success = Object.values(results).every(result => result === true);
    
    console.log("🧪 Test completato:", {
      success,
      results,
      errors: errors.length
    });

    return { success, results, errors };

  } catch (error) {
    errors.push(`Errore generale test: ${error}`);
    return { success: false, results, errors };
  }
};

/**
 * Test di performance per verificare l'impatto sul gioco
 */
export const runPerformanceTest = async (): Promise<{
  success: boolean;
  metrics: {
    collectionOverhead: number; // ms
    compressionTime: number; // ms
    uploadTime: number; // ms
    memoryUsage: number; // MB stimati
  };
  errors: string[];
}> => {
  const errors: string[] = [];
  const metrics = {
    collectionOverhead: 0,
    compressionTime: 0,
    uploadTime: 0,
    memoryUsage: 0
  };

  try {
    console.log("⚡ Iniziando test performance...");

    // Test overhead raccolta dati
    const gameState = initializeGameState(31);
    const mockCard: Card = { 
      id: "perf-test", 
      suit: Suit.Coins, 
      rank: "A", 
      displayName: "Performance Test Card" 
    };

    const startCollection = performance.now();
    gameDataCollector.startCollection(gameState);
    
    // Simula 40 mosse (partita completa)
    for (let i = 0; i < 40; i++) {
      gameDataCollector.recordMove(
        gameState,
        i % 4,
        mockCard,
        [mockCard],
        []
      );
      
      if ((i + 1) % 4 === 0) {
        gameDataCollector.updateLastMoveWithTrickResult(0, 1);
      }
    }
    
    const endCollection = performance.now();
    metrics.collectionOverhead = endCollection - startCollection;

    // Test compressione
    const finalGameState = {
      ...gameState,
      gameScore: [21, 10] as [number, number],
      gamePhase: "gameOver" as const
    };

    const startCompression = performance.now();
    const trainingData = gameDataCollector.generateTrainingData(finalGameState);
    const jsonString = JSON.stringify(trainingData);
    const compressedData = btoa(jsonString);
    const endCompression = performance.now();
    
    metrics.compressionTime = endCompression - startCompression;
    metrics.memoryUsage = Math.round(jsonString.length / 1024); // KB to MB approximation

    // Test upload
    if (trainingData) {
      const startUpload = performance.now();
      const uploadResult = await collectGameData(trainingData);
      const endUpload = performance.now();
      
      metrics.uploadTime = endUpload - startUpload;
      
      if (!uploadResult.success) {
        errors.push(`Upload fallito: ${uploadResult.error}`);
      }
    }

    // Cleanup
    gameDataCollector.reset();

    // Verifica soglie performance
    const success = 
      metrics.collectionOverhead < 100 && // <100ms per raccolta completa
      metrics.compressionTime < 50 && // <50ms per compressione
      metrics.uploadTime < 10000 && // <10s per upload
      metrics.memoryUsage < 1; // <1MB memoria

    console.log("⚡ Test performance completato:", {
      success,
      metrics,
      thresholds: {
        collectionOverhead: "< 100ms",
        compressionTime: "< 50ms", 
        uploadTime: "< 10s",
        memoryUsage: "< 1MB"
      }
    });

    return { success, metrics, errors };

  } catch (error) {
    errors.push(`Errore test performance: ${error}`);
    return { success: false, metrics, errors };
  }
};
