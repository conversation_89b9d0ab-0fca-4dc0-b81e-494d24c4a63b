/**
 * 🚀 FAST TRAINING AI
 * AI ottimizzata per auto-training veloce con tutte le funzionalità del gioco
 * Include cooperazione, comunicazione (busso, striscio, volo) e strategia avanzata
 */

import { GameState } from "@/utils/game/gameLogic";
import { Card, Suit, Rank } from "@/utils/game/cardUtils";
import { getAIMove, AIDifficulty } from "@/utils/ai";

export interface FastAIMove {
  card: Card;
  communication?: "busso" | "striscio" | "volo" | null;
  reasoning: string;
}

/**
 * AI ottimizzata per training veloce con comunicazione avanzata
 */
export class FastTrainingAI {
  private difficulty: AIDifficulty;
  private playerIndex: number;

  constructor(difficulty: AIDifficulty = AIDifficulty.HARD) {
    this.difficulty = difficulty;
    this.playerIndex = 0;
  }

  /**
   * Ottiene la mossa migliore per il giocatore corrente
   */
  public getMove(gameState: GameState, playerIndex: number): FastAIMove {
    this.playerIndex = playerIndex;
    const player = gameState.players[playerIndex];
    const hand = player.hand;

    // Usa l'AI esistente come base
    const baseMove = getAIMove(gameState, playerIndex, this.difficulty);
    
    if (!baseMove) {
      // Fallback: gioca la prima carta disponibile
      return {
        card: hand[0],
        communication: null,
        reasoning: "Fallback: nessuna mossa AI disponibile"
      };
    }

    // Analizza la situazione per comunicazione
    const communication = this.analyzeCommunication(gameState, playerIndex, baseMove.card);
    
    // Analizza il ragionamento strategico
    const reasoning = this.analyzeReasoning(gameState, playerIndex, baseMove.card);

    return {
      card: baseMove.card,
      communication,
      reasoning
    };
  }

  /**
   * Analizza se è necessaria comunicazione con il compagno
   */
  private analyzeCommunication(
    gameState: GameState, 
    playerIndex: number, 
    card: Card
  ): "busso" | "striscio" | "volo" | null {
    const player = gameState.players[playerIndex];
    const hand = player.hand;
    const trumpSuit = gameState.trumpSuit;
    const currentTrick = gameState.currentTrick;
    const isFirstCard = currentTrick.length === 0;

    // BUSSO: Segnala che hai carte forti del seme giocato
    if (this.shouldBusso(gameState, playerIndex, card)) {
      return "busso";
    }

    // STRISCIO: Segnala che non hai carte del seme richiesto
    if (this.shouldStriscio(gameState, playerIndex, card)) {
      return "striscio";
    }

    // VOLO: Segnala che hai briscole forti
    if (this.shouldVolo(gameState, playerIndex, card)) {
      return "volo";
    }

    return null;
  }

  /**
   * Determina se fare "busso" (ho carte forti di questo seme)
   */
  private shouldBusso(gameState: GameState, playerIndex: number, card: Card): boolean {
    const player = gameState.players[playerIndex];
    const hand = player.hand;
    const currentTrick = gameState.currentTrick;
    const isFirstCard = currentTrick.length === 0;

    // Busso solo se gioco per primo e ho carte forti del seme
    if (!isFirstCard) return false;

    const sameSuitCards = hand.filter(c => c.suit === card.suit);
    const strongCards = sameSuitCards.filter(c => 
      c.rank === "A" || c.rank === "3" || c.rank === "K" || c.rank === "Q" || c.rank === "J"
    );

    // Busso se ho almeno 2 carte forti del seme che sto giocando
    return strongCards.length >= 2;
  }

  /**
   * Determina se fare "striscio" (non ho carte del seme richiesto)
   */
  private shouldStriscio(gameState: GameState, playerIndex: number, card: Card): boolean {
    const player = gameState.players[playerIndex];
    const hand = player.hand;
    const currentTrick = gameState.currentTrick;
    const leadSuit = gameState.leadSuit;

    // Striscio solo se non sono il primo a giocare
    if (currentTrick.length === 0) return false;
    if (!leadSuit) return false;

    // Striscio se non ho carte del seme richiesto e sto giocando una carta diversa
    const hasLeadSuit = hand.some(c => c.suit === leadSuit);
    const playingDifferentSuit = card.suit !== leadSuit;

    return !hasLeadSuit && playingDifferentSuit;
  }

  /**
   * Determina se fare "volo" (ho briscole forti)
   */
  private shouldVolo(gameState: GameState, playerIndex: number, card: Card): boolean {
    const player = gameState.players[playerIndex];
    const hand = player.hand;
    const trumpSuit = gameState.trumpSuit;

    if (!trumpSuit) return false;

    // Volo se sto giocando una briscola forte
    if (card.suit === trumpSuit && (card.rank === "A" || card.rank === "3")) {
      return true;
    }

    // Volo se ho molte briscole forti in mano
    const trumpCards = hand.filter(c => c.suit === trumpSuit);
    const strongTrumps = trumpCards.filter(c => 
      c.rank === "A" || c.rank === "3" || c.rank === "K"
    );

    return strongTrumps.length >= 3;
  }

  /**
   * Analizza il ragionamento strategico della mossa
   */
  private analyzeReasoning(gameState: GameState, playerIndex: number, card: Card): string {
    const player = gameState.players[playerIndex];
    const hand = player.hand;
    const trumpSuit = gameState.trumpSuit;
    const currentTrick = gameState.currentTrick;
    const leadSuit = gameState.leadSuit;
    const isFirstCard = currentTrick.length === 0;

    // Analisi situazione Maraffa
    if (this.hasMaraffa(hand, trumpSuit) && card.suit === trumpSuit && card.rank === "A") {
      return "Gioco Asso di briscola per Maraffa (+3 punti)";
    }

    // Analisi apertura
    if (isFirstCard) {
      if (card.rank === "3" || card.rank === "2") {
        return "Apertura sicura con carta bassa";
      }
      if (card.suit === trumpSuit) {
        return "Apertura con briscola per controllare il gioco";
      }
      return "Apertura strategica";
    }

    // Analisi risposta
    if (leadSuit) {
      if (card.suit === leadSuit) {
        if (this.isWinningCard(gameState, card)) {
          return "Prendo la presa con carta vincente";
        }
        return "Seguo il seme richiesto";
      }
      
      if (card.suit === trumpSuit) {
        return "Taglio con briscola";
      }
      
      return "Scarto carta non utile";
    }

    return "Mossa strategica";
  }

  /**
   * Verifica se il giocatore ha la Maraffa
   */
  private hasMaraffa(hand: Card[], trumpSuit: Suit | null): boolean {
    if (!trumpSuit) return false;
    
    const requiredRanks = ["A", "2", "3"];
    return requiredRanks.every(rank =>
      hand.some(card => card.suit === trumpSuit && card.rank === rank)
    );
  }

  /**
   * Verifica se una carta può vincere la presa corrente
   */
  private isWinningCard(gameState: GameState, card: Card): boolean {
    const currentTrick = gameState.currentTrick;
    const trumpSuit = gameState.trumpSuit;
    const leadSuit = gameState.leadSuit;

    if (currentTrick.length === 0) return true;

    // Logica semplificata per determinare se la carta vince
    const cardValue = this.getCardValue(card, trumpSuit, leadSuit);
    
    for (const trickCard of currentTrick) {
      const trickCardValue = this.getCardValue(trickCard.card, trumpSuit, leadSuit);
      if (trickCardValue >= cardValue) {
        return false;
      }
    }

    return true;
  }

  /**
   * Calcola il valore di una carta per il confronto
   */
  private getCardValue(card: Card, trumpSuit: Suit | null, leadSuit: Suit | null): number {
    let baseValue = 0;

    // Valori base delle carte
    switch (card.rank) {
      case "A": baseValue = 14; break;
      case "3": baseValue = 13; break;
      case "K": baseValue = 12; break;
      case "Q": baseValue = 11; break;
      case "J": baseValue = 10; break;
      case "7": baseValue = 7; break;
      case "6": baseValue = 6; break;
      case "5": baseValue = 5; break;
      case "4": baseValue = 4; break;
      case "2": baseValue = 2; break;
      default: baseValue = 1;
    }

    // Bonus per briscola
    if (trumpSuit && card.suit === trumpSuit) {
      baseValue += 100;
    }
    // Bonus per seme richiesto
    else if (leadSuit && card.suit === leadSuit) {
      baseValue += 50;
    }

    return baseValue;
  }

  /**
   * Analizza la situazione di gioco per strategie avanzate
   */
  public analyzeGameSituation(gameState: GameState, playerIndex: number): {
    teamScore: [number, number];
    isWinning: boolean;
    needsAggressive: boolean;
    partnerPosition: number;
    opponentsPositions: number[];
  } {
    const player = gameState.players[playerIndex];
    const teamScore = gameState.gameScore;
    const playerTeam = player.team;
    const isWinning = teamScore[playerTeam] > teamScore[playerTeam === 0 ? 1 : 0];
    const scoreDifference = Math.abs(teamScore[0] - teamScore[1]);
    const needsAggressive = !isWinning && scoreDifference > 5;

    // Trova posizioni compagno e avversari
    const partnerPosition = gameState.players.findIndex(p => 
      p.team === playerTeam && gameState.players.indexOf(p) !== playerIndex
    );
    
    const opponentsPositions = gameState.players
      .map((p, index) => ({ player: p, index }))
      .filter(({ player }) => player.team !== playerTeam)
      .map(({ index }) => index);

    return {
      teamScore,
      isWinning,
      needsAggressive,
      partnerPosition,
      opponentsPositions
    };
  }
}

/**
 * Funzione di utilità per ottenere una mossa veloce
 */
export const getFastTrainingMove = (
  gameState: GameState, 
  playerIndex: number, 
  difficulty: AIDifficulty = AIDifficulty.HARD
): FastAIMove => {
  const ai = new FastTrainingAI(difficulty);
  return ai.getMove(gameState, playerIndex);
};

/**
 * Funzione per simulare comunicazione tra compagni
 */
export const simulateTeamCommunication = (
  gameState: GameState,
  playerIndex: number,
  communication: "busso" | "striscio" | "volo" | null
): void => {
  if (!communication) return;

  const player = gameState.players[playerIndex];
  const partnerIndex = gameState.players.findIndex(p => 
    p.team === player.team && gameState.players.indexOf(p) !== playerIndex
  );

  if (partnerIndex === -1) return;

  // Simula la comprensione del compagno
  console.log(`🤝 Comunicazione: ${player.name} fa "${communication}" al compagno ${gameState.players[partnerIndex].name}`);
  
  // In una implementazione completa, questo influenzerebbe le decisioni future del compagno
};
