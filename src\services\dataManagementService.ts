/**
 * 📁 DATA MANAGEMENT SERVICE
 * Servizio per gestire backup, export e pulizia dei dati Community AI
 */

import { supabase } from "@/integrations/supabase/client";
import { getCommunityAIStats } from "@/services/communityAIService";
import { trainingAnalytics } from "@/services/trainingAnalyticsService";

/**
 * Interfacce per la gestione dati
 */
export interface ExportOptions {
  includeRawData: boolean;
  includeAnalytics: boolean;
  includeStats: boolean;
  dateRange?: {
    from: Date;
    to: Date;
  };
  format: "json" | "csv";
}

export interface BackupResult {
  success: boolean;
  fileCount: number;
  totalSize: number; // in bytes
  backupId: string;
  error?: string;
}

export interface CleanupOptions {
  olderThanDays: number;
  keepMinimumFiles: number;
  dryRun: boolean; // Se true, mostra solo cosa verrebbe eliminato
}

export interface CleanupResult {
  success: boolean;
  filesDeleted: number;
  spaceFreed: number; // in bytes
  filesKept: number;
  error?: string;
}

/**
 * Classe per la gestione dei dati
 */
export class DataManagementService {
  private readonly BUCKET_NAME = "ai-training-data";

  /**
   * Esporta tutti i dati in formato specificato
   */
  public async exportData(options: ExportOptions): Promise<{
    success: boolean;
    data?: string;
    filename?: string;
    error?: string;
  }> {
    try {
      console.log("📁 DataManagement: Iniziando export dati...", options);

      const exportData: any = {
        exportInfo: {
          timestamp: new Date().toISOString(),
          options,
          version: "1.0"
        }
      };

      // Include statistiche Community AI
      if (options.includeStats) {
        exportData.communityAIStats = getCommunityAIStats();
      }

      // Include analytics
      if (options.includeAnalytics) {
        exportData.trainingAnalytics = {
          metrics: await trainingAnalytics.getTrainingMetrics(),
          progress: await trainingAnalytics.getTrainingProgress(),
          patterns: await trainingAnalytics.analyzeGamePatterns()
        };
      }

      // Include dati raw (se richiesto e disponibile)
      if (options.includeRawData) {
        try {
          const rawData = await this.fetchRawDataFromStorage(options.dateRange);
          exportData.rawGameData = rawData;
        } catch (error) {
          console.warn("⚠️ DataManagement: Errore recupero dati raw:", error);
          exportData.rawGameData = {
            error: "Impossibile recuperare dati raw",
            reason: error instanceof Error ? error.message : "Errore sconosciuto"
          };
        }
      }

      // Genera filename
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `maraffa-ai-export-${timestamp}.${options.format}`;

      // Converte in formato richiesto
      let dataString: string;
      if (options.format === "json") {
        dataString = JSON.stringify(exportData, null, 2);
      } else {
        // CSV semplificato (solo statistiche)
        dataString = this.convertToCSV(exportData);
      }

      console.log("✅ DataManagement: Export completato", {
        size: `${Math.round(dataString.length / 1024)}KB`,
        filename
      });

      return {
        success: true,
        data: dataString,
        filename
      };

    } catch (error) {
      console.error("❌ DataManagement: Errore export:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Errore sconosciuto"
      };
    }
  }

  /**
   * Recupera dati raw dal storage Supabase
   */
  private async fetchRawDataFromStorage(dateRange?: { from: Date; to: Date }): Promise<any[]> {
    const { data: files, error } = await supabase.storage
      .from(this.BUCKET_NAME)
      .list();

    if (error) {
      throw new Error(`Errore accesso storage: ${error.message}`);
    }

    if (!files || files.length === 0) {
      return [];
    }

    // Filtra per data se specificato
    let filteredFiles = files;
    if (dateRange) {
      filteredFiles = files.filter(file => {
        const fileDate = new Date(file.created_at);
        return fileDate >= dateRange.from && fileDate <= dateRange.to;
      });
    }

    // Limita a massimo 100 file per evitare timeout
    const filesToProcess = filteredFiles.slice(0, 100);
    const rawData: any[] = [];

    for (const file of filesToProcess) {
      try {
        const { data: fileData, error: downloadError } = await supabase.storage
          .from(this.BUCKET_NAME)
          .download(file.name);

        if (downloadError) {
          console.warn(`⚠️ Errore download ${file.name}:`, downloadError);
          continue;
        }

        // Decomprime e parsifica (semplificato)
        const text = await fileData.text();
        const decompressed = atob(text); // Reverse della compressione base64
        const gameData = JSON.parse(decompressed);
        
        rawData.push({
          filename: file.name,
          uploadDate: file.created_at,
          data: gameData
        });

      } catch (error) {
        console.warn(`⚠️ Errore processamento ${file.name}:`, error);
      }
    }

    return rawData;
  }

  /**
   * Converte dati in formato CSV
   */
  private convertToCSV(data: any): string {
    const lines: string[] = [];
    
    // Header CSV
    lines.push("Tipo,Chiave,Valore,Timestamp");
    
    // Statistiche Community AI
    if (data.communityAIStats) {
      const stats = data.communityAIStats;
      lines.push(`Stats,TotalGamesCollected,${stats.totalGamesCollected},${data.exportInfo.timestamp}`);
      lines.push(`Stats,SuccessfulUploads,${stats.successfulUploads},${data.exportInfo.timestamp}`);
      lines.push(`Stats,UploadErrors,${stats.uploadErrors},${data.exportInfo.timestamp}`);
      lines.push(`Stats,TotalDataUploaded,${stats.totalDataUploaded},${data.exportInfo.timestamp}`);
    }

    // Analytics (semplificato)
    if (data.trainingAnalytics?.metrics) {
      const metrics = data.trainingAnalytics.metrics;
      lines.push(`Analytics,TotalGames,${metrics.totalGames},${data.exportInfo.timestamp}`);
      lines.push(`Analytics,TotalMoves,${metrics.totalMoves},${data.exportInfo.timestamp}`);
      lines.push(`Analytics,AverageGameDuration,${metrics.averageGameDuration},${data.exportInfo.timestamp}`);
      lines.push(`Analytics,HumanPlayerWinRate,${metrics.playerPatterns.humanPlayerWinRate},${data.exportInfo.timestamp}`);
    }

    return lines.join('\n');
  }

  /**
   * Crea un backup completo dei dati
   */
  public async createBackup(): Promise<BackupResult> {
    try {
      console.log("📁 DataManagement: Creando backup...");

      const { data: files, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list();

      if (error) {
        return {
          success: false,
          fileCount: 0,
          totalSize: 0,
          backupId: "",
          error: `Errore accesso storage: ${error.message}`
        };
      }

      const fileCount = files?.length || 0;
      const totalSize = files?.reduce((sum, file) => sum + (file.metadata?.size || 0), 0) || 0;
      const backupId = `backup_${Date.now()}`;

      // In un'implementazione reale, qui si copierebbe tutto in un bucket di backup
      // Per ora simuliamo il backup
      console.log("✅ DataManagement: Backup simulato completato", {
        fileCount,
        totalSize: `${Math.round(totalSize / 1024)}KB`,
        backupId
      });

      return {
        success: true,
        fileCount,
        totalSize,
        backupId
      };

    } catch (error) {
      console.error("❌ DataManagement: Errore backup:", error);
      return {
        success: false,
        fileCount: 0,
        totalSize: 0,
        backupId: "",
        error: error instanceof Error ? error.message : "Errore sconosciuto"
      };
    }
  }

  /**
   * Pulisce dati vecchi secondo i criteri specificati
   */
  public async cleanupOldData(options: CleanupOptions): Promise<CleanupResult> {
    try {
      console.log("📁 DataManagement: Iniziando pulizia dati...", options);

      const { data: files, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list();

      if (error) {
        return {
          success: false,
          filesDeleted: 0,
          spaceFreed: 0,
          filesKept: 0,
          error: `Errore accesso storage: ${error.message}`
        };
      }

      if (!files || files.length === 0) {
        return {
          success: true,
          filesDeleted: 0,
          spaceFreed: 0,
          filesKept: 0
        };
      }

      // Calcola data limite
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - options.olderThanDays);

      // Filtra file vecchi
      const oldFiles = files.filter(file => {
        const fileDate = new Date(file.created_at);
        return fileDate < cutoffDate;
      });

      // Ordina per data (più vecchi prima)
      oldFiles.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

      // Determina quanti file eliminare (mantenendo il minimo richiesto)
      const totalFiles = files.length;
      const maxToDelete = Math.max(0, totalFiles - options.keepMinimumFiles);
      const filesToDelete = oldFiles.slice(0, Math.min(oldFiles.length, maxToDelete));

      let filesDeleted = 0;
      let spaceFreed = 0;

      if (!options.dryRun && filesToDelete.length > 0) {
        // Elimina file in batch
        const fileNames = filesToDelete.map(f => f.name);
        const { error: deleteError } = await supabase.storage
          .from(this.BUCKET_NAME)
          .remove(fileNames);

        if (deleteError) {
          throw new Error(`Errore eliminazione: ${deleteError.message}`);
        }

        filesDeleted = filesToDelete.length;
        spaceFreed = filesToDelete.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);
      } else {
        // Dry run - simula eliminazione
        filesDeleted = filesToDelete.length;
        spaceFreed = filesToDelete.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);
      }

      const filesKept = totalFiles - filesDeleted;

      console.log("✅ DataManagement: Pulizia completata", {
        filesDeleted,
        spaceFreed: `${Math.round(spaceFreed / 1024)}KB`,
        filesKept,
        dryRun: options.dryRun
      });

      return {
        success: true,
        filesDeleted,
        spaceFreed,
        filesKept
      };

    } catch (error) {
      console.error("❌ DataManagement: Errore pulizia:", error);
      return {
        success: false,
        filesDeleted: 0,
        spaceFreed: 0,
        filesKept: 0,
        error: error instanceof Error ? error.message : "Errore sconosciuto"
      };
    }
  }

  /**
   * Ottiene statistiche dello storage
   */
  public async getStorageStats(): Promise<{
    success: boolean;
    totalFiles: number;
    totalSize: number;
    oldestFile?: string;
    newestFile?: string;
    error?: string;
  }> {
    try {
      const { data: files, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list();

      if (error) {
        return {
          success: false,
          totalFiles: 0,
          totalSize: 0,
          error: error.message
        };
      }

      if (!files || files.length === 0) {
        return {
          success: true,
          totalFiles: 0,
          totalSize: 0
        };
      }

      const totalFiles = files.length;
      const totalSize = files.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);
      
      const sortedByDate = [...files].sort((a, b) => 
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );

      return {
        success: true,
        totalFiles,
        totalSize,
        oldestFile: sortedByDate[0]?.created_at,
        newestFile: sortedByDate[sortedByDate.length - 1]?.created_at
      };

    } catch (error) {
      return {
        success: false,
        totalFiles: 0,
        totalSize: 0,
        error: error instanceof Error ? error.message : "Errore sconosciuto"
      };
    }
  }
}

/**
 * Istanza singleton del servizio
 */
export const dataManagement = new DataManagementService();
