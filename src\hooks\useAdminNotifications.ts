/**
 * 🔔 ADMIN NOTIFICATIONS HOOK
 * Hook per gestire notifiche nel pannello admin
 */

import { useState, useEffect, useCallback } from "react";
import { getCommunityAIStats } from "@/services/communityAIService";
import { trainingAnalytics } from "@/services/trainingAnalyticsService";

export interface AdminNotification {
  id: string;
  type: "info" | "warning" | "error" | "success";
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export const useAdminNotifications = () => {
  const [notifications, setNotifications] = useState<AdminNotification[]>([]);
  const [lastCheck, setLastCheck] = useState<Date>(new Date());

  // Genera notifiche basate sullo stato del sistema
  const generateSystemNotifications = useCallback(async (): Promise<AdminNotification[]> => {
    const newNotifications: AdminNotification[] = [];
    const now = new Date();

    try {
      // Controlla statistiche Community AI
      const stats = getCommunityAIStats();
      
      // Notifica se ci sono molti errori di upload
      if (stats.uploadErrors > 0 && stats.totalGamesCollected > 0) {
        const errorRate = (stats.uploadErrors / stats.totalGamesCollected) * 100;
        if (errorRate > 10) {
          newNotifications.push({
            id: `upload-errors-${now.getTime()}`,
            type: "warning",
            title: "Alto tasso di errori upload",
            message: `${errorRate.toFixed(1)}% degli upload fallisce. Controlla la connessione Supabase.`,
            timestamp: now,
            read: false,
            action: {
              label: "Vai a Debug",
              onClick: () => {
                // Scroll al tab debug
                const debugTab = document.querySelector('[value="debug"]');
                if (debugTab) {
                  (debugTab as HTMLElement).click();
                }
              }
            }
          });
        }
      }

      // Controlla progresso training
      const progress = await trainingAnalytics.getTrainingProgress();
      
      // Notifica quando si raggiungono milestone importanti
      if (progress.currentMetrics) {
        const totalGames = progress.currentMetrics.totalGames;
        
        if (totalGames >= 100 && totalGames < 105) {
          newNotifications.push({
            id: `milestone-100-${now.getTime()}`,
            type: "success",
            title: "Milestone raggiunta!",
            message: "Raccolte 100+ partite. Il training AI può iniziare!",
            timestamp: now,
            read: false,
            action: {
              label: "Vai a Training",
              onClick: () => {
                const trainingTab = document.querySelector('[value="training"]');
                if (trainingTab) {
                  (trainingTab as HTMLElement).click();
                }
              }
            }
          });
        }

        if (totalGames >= 500 && totalGames < 505) {
          newNotifications.push({
            id: `milestone-500-${now.getTime()}`,
            type: "success",
            title: "Dataset completo!",
            message: "Raccolte 500+ partite. Dataset pronto per training avanzato!",
            timestamp: now,
            read: false,
            action: {
              label: "Configura AI",
              onClick: () => {
                const settingsTab = document.querySelector('[value="settings"]');
                if (settingsTab) {
                  (settingsTab as HTMLElement).click();
                }
              }
            }
          });
        }

        // Notifica se la qualità dei dati è bassa
        if (progress.currentMetrics.dataQuality.qualityScore < 60) {
          newNotifications.push({
            id: `low-quality-${now.getTime()}`,
            type: "warning",
            title: "Qualità dati bassa",
            message: `Score qualità: ${progress.currentMetrics.dataQuality.qualityScore}%. Controlla il sistema di raccolta.`,
            timestamp: now,
            read: false
          });
        }
      }

      // Notifica se non ci sono stati upload recenti
      if (stats.lastSuccessfulUpload) {
        const lastUpload = new Date(stats.lastSuccessfulUpload);
        const hoursSinceLastUpload = (now.getTime() - lastUpload.getTime()) / (1000 * 60 * 60);
        
        if (hoursSinceLastUpload > 24) {
          newNotifications.push({
            id: `no-recent-uploads-${now.getTime()}`,
            type: "info",
            title: "Nessun upload recente",
            message: `Ultimo upload: ${Math.floor(hoursSinceLastUpload)}h fa. Il sistema potrebbe essere inattivo.`,
            timestamp: now,
            read: false
          });
        }
      }

    } catch (error) {
      console.warn("⚠️ AdminNotifications: Errore generazione notifiche:", error);
    }

    return newNotifications;
  }, []);

  // Controlla per nuove notifiche
  const checkForNotifications = useCallback(async () => {
    const newNotifications = await generateSystemNotifications();
    
    if (newNotifications.length > 0) {
      setNotifications(prev => {
        // Evita duplicati basandosi sull'ID
        const existingIds = new Set(prev.map(n => n.id));
        const uniqueNew = newNotifications.filter(n => !existingIds.has(n.id));
        
        if (uniqueNew.length > 0) {
          return [...prev, ...uniqueNew].slice(-20); // Mantieni solo le ultime 20
        }
        
        return prev;
      });
    }
    
    setLastCheck(new Date());
  }, [generateSystemNotifications]);

  // Controlla periodicamente per nuove notifiche
  useEffect(() => {
    // Controlla immediatamente
    checkForNotifications();
    
    // Poi ogni 5 minuti
    const interval = setInterval(checkForNotifications, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [checkForNotifications]);

  // Marca una notifica come letta
  const markAsRead = useCallback((id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  }, []);

  // Marca tutte le notifiche come lette
  const markAllAsRead = useCallback(() => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    );
  }, []);

  // Rimuove una notifica
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // Pulisce tutte le notifiche
  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  // Forza un controllo manuale
  const refresh = useCallback(() => {
    checkForNotifications();
  }, [checkForNotifications]);

  // Statistiche notifiche
  const unreadCount = notifications.filter(n => !n.read).length;
  const hasUnread = unreadCount > 0;
  const latestNotification = notifications.length > 0 ? notifications[notifications.length - 1] : null;

  return {
    notifications,
    unreadCount,
    hasUnread,
    latestNotification,
    lastCheck,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    refresh
  };
};
