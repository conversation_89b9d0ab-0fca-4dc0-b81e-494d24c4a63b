/**
 * 📊 TRAINING ANALYTICS SERVICE
 * Servizio per analizzare i dati di training e generare statistiche avanzate
 */

import { supabase } from "@/integrations/supabase/client";
import type { GameTrainingData } from "@/types/communityAI";

/**
 * Interfacce per le statistiche di training
 */
export interface TrainingMetrics {
  totalGames: number;
  totalMoves: number;
  averageGameDuration: number;
  winRateByDifficulty: {
    easy: number;
    medium: number;
    hard: number;
  };
  mostPlayedCards: Array<{
    card: string;
    count: number;
    winRate: number;
  }>;
  playerPatterns: {
    humanPlayerWinRate: number;
    averageMovesPerGame: number;
    commonOpeningMoves: Array<{
      move: string;
      frequency: number;
    }>;
  };
  dataQuality: {
    completeGames: number;
    incompleteGames: number;
    averageDataSize: number;
    qualityScore: number; // 0-100
  };
}

export interface TrainingProgress {
  phase: "collecting" | "preprocessing" | "training" | "validation" | "ready";
  progress: number; // 0-100
  estimatedCompletion?: Date;
  currentMetrics?: TrainingMetrics;
  recommendations: string[];
}

/**
 * Classe per l'analisi dei dati di training
 */
export class TrainingAnalyticsService {
  private cachedMetrics: TrainingMetrics | null = null;
  private lastCacheUpdate: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minuti

  /**
   * Ottiene le metriche di training dal cache o le ricalcola
   */
  public async getTrainingMetrics(forceRefresh = false): Promise<TrainingMetrics> {
    const now = Date.now();
    
    if (!forceRefresh && 
        this.cachedMetrics && 
        (now - this.lastCacheUpdate) < this.CACHE_DURATION) {
      return this.cachedMetrics;
    }

    console.log("📊 TrainingAnalytics: Calcolando metriche...");
    
    try {
      // In un'implementazione reale, qui caricheremmo i dati da Supabase
      // Per ora simuliamo con dati mock
      const metrics = await this.calculateMetricsFromStorage();
      
      this.cachedMetrics = metrics;
      this.lastCacheUpdate = now;
      
      return metrics;
    } catch (error) {
      console.error("❌ TrainingAnalytics: Errore calcolo metriche:", error);
      
      // Ritorna metriche vuote in caso di errore
      return this.getEmptyMetrics();
    }
  }

  /**
   * Calcola le metriche dai dati in storage
   */
  private async calculateMetricsFromStorage(): Promise<TrainingMetrics> {
    try {
      // Tenta di recuperare i file dal bucket Supabase
      const { data: files, error } = await supabase.storage
        .from('ai-training-data')
        .list();

      if (error) {
        console.warn("⚠️ TrainingAnalytics: Errore accesso storage:", error);
        return this.getEmptyMetrics();
      }

      const totalFiles = files?.length || 0;
      
      // Per ora generiamo metriche simulate basate sul numero di file
      return this.generateSimulatedMetrics(totalFiles);
      
    } catch (error) {
      console.warn("⚠️ TrainingAnalytics: Fallback a metriche simulate");
      return this.generateSimulatedMetrics(0);
    }
  }

  /**
   * Genera metriche simulate per testing
   */
  private generateSimulatedMetrics(fileCount: number): TrainingMetrics {
    const totalGames = Math.max(fileCount, 0);
    const totalMoves = totalGames * 35; // Media 35 mosse per partita
    
    return {
      totalGames,
      totalMoves,
      averageGameDuration: 4.5 * 60 * 1000, // 4.5 minuti
      winRateByDifficulty: {
        easy: 75 + Math.random() * 10,
        medium: 55 + Math.random() * 10,
        hard: 35 + Math.random() * 10
      },
      mostPlayedCards: [
        { card: "Asso di Denari", count: Math.floor(totalGames * 0.8), winRate: 65 },
        { card: "Re di Coppe", count: Math.floor(totalGames * 0.7), winRate: 45 },
        { card: "3 di Spade", count: Math.floor(totalGames * 0.6), winRate: 55 },
        { card: "Cavallo di Bastoni", count: Math.floor(totalGames * 0.5), winRate: 40 },
        { card: "2 di Denari", count: Math.floor(totalGames * 0.9), winRate: 30 }
      ],
      playerPatterns: {
        humanPlayerWinRate: 58 + Math.random() * 10,
        averageMovesPerGame: 35 + Math.random() * 5,
        commonOpeningMoves: [
          { move: "3 di Denari", frequency: 25 },
          { move: "2 di Coppe", frequency: 18 },
          { move: "Asso di Spade", frequency: 15 },
          { move: "Re di Bastoni", frequency: 12 },
          { move: "Cavallo di Denari", frequency: 10 }
        ]
      },
      dataQuality: {
        completeGames: Math.floor(totalGames * 0.95),
        incompleteGames: Math.floor(totalGames * 0.05),
        averageDataSize: 45 + Math.random() * 15, // KB
        qualityScore: Math.min(100, 60 + (totalGames / 10)) // Migliora con più dati
      }
    };
  }

  /**
   * Ritorna metriche vuote
   */
  private getEmptyMetrics(): TrainingMetrics {
    return {
      totalGames: 0,
      totalMoves: 0,
      averageGameDuration: 0,
      winRateByDifficulty: { easy: 0, medium: 0, hard: 0 },
      mostPlayedCards: [],
      playerPatterns: {
        humanPlayerWinRate: 0,
        averageMovesPerGame: 0,
        commonOpeningMoves: []
      },
      dataQuality: {
        completeGames: 0,
        incompleteGames: 0,
        averageDataSize: 0,
        qualityScore: 0
      }
    };
  }

  /**
   * Calcola il progresso del training
   */
  public async getTrainingProgress(): Promise<TrainingProgress> {
    const metrics = await this.getTrainingMetrics();
    
    // Determina la fase basata sui dati disponibili
    let phase: TrainingProgress['phase'] = "collecting";
    let progress = 0;
    const recommendations: string[] = [];

    if (metrics.totalGames < 50) {
      phase = "collecting";
      progress = (metrics.totalGames / 50) * 100;
      recommendations.push("Raccogli almeno 50 partite per iniziare l'analisi preliminare");
    } else if (metrics.totalGames < 100) {
      phase = "preprocessing";
      progress = 50 + ((metrics.totalGames - 50) / 50) * 30;
      recommendations.push("Continua a raccogliere dati per migliorare la qualità del training");
    } else if (metrics.totalGames < 500) {
      phase = "training";
      progress = 80 + ((metrics.totalGames - 100) / 400) * 15;
      recommendations.push("Dati sufficienti per iniziare il training dell'AI");
      recommendations.push("Considera di bilanciare i dati per difficoltà diverse");
    } else {
      phase = "ready";
      progress = 100;
      recommendations.push("Dataset pronto per training avanzato");
      recommendations.push("Considera l'implementazione dell'AI addestrata");
    }

    // Aggiungi raccomandazioni basate sulla qualità
    if (metrics.dataQuality.qualityScore < 70) {
      recommendations.push("Migliora la qualità dei dati riducendo partite incomplete");
    }

    if (metrics.dataQuality.incompleteGames > metrics.totalGames * 0.1) {
      recommendations.push("Troppi dati incompleti - verifica il sistema di raccolta");
    }

    return {
      phase,
      progress: Math.min(100, Math.max(0, progress)),
      currentMetrics: metrics,
      recommendations
    };
  }

  /**
   * Analizza i pattern di gioco per identificare strategie
   */
  public async analyzeGamePatterns(): Promise<{
    winningStrategies: Array<{
      strategy: string;
      winRate: number;
      frequency: number;
    }>;
    commonMistakes: Array<{
      mistake: string;
      frequency: number;
      impact: number;
    }>;
    aiRecommendations: string[];
  }> {
    const metrics = await this.getTrainingMetrics();
    
    return {
      winningStrategies: [
        {
          strategy: "Apertura con carte basse quando si ha la briscola",
          winRate: 68,
          frequency: 45
        },
        {
          strategy: "Conservare gli assi per i trick finali",
          winRate: 72,
          frequency: 38
        },
        {
          strategy: "Giocare figure quando il compagno è in vantaggio",
          winRate: 65,
          frequency: 52
        }
      ],
      commonMistakes: [
        {
          mistake: "Giocare assi troppo presto",
          frequency: 32,
          impact: 15 // Punti persi in media
        },
        {
          mistake: "Non seguire il seme quando possibile",
          frequency: 18,
          impact: 8
        },
        {
          mistake: "Sprecare carte di briscola su trick già vinti",
          frequency: 25,
          impact: 12
        }
      ],
      aiRecommendations: [
        "Implementa logica per conservare carte di valore",
        "Migliora la valutazione del timing per giocare gli assi",
        "Aggiungi analisi del contesto di squadra",
        "Considera la memoria delle carte già giocate"
      ]
    };
  }

  /**
   * Pulisce la cache
   */
  public clearCache(): void {
    this.cachedMetrics = null;
    this.lastCacheUpdate = 0;
    console.log("📊 TrainingAnalytics: Cache pulita");
  }

  /**
   * Esporta le metriche in formato JSON
   */
  public async exportMetrics(): Promise<string> {
    const metrics = await this.getTrainingMetrics();
    const progress = await this.getTrainingProgress();
    const patterns = await this.analyzeGamePatterns();
    
    const exportData = {
      timestamp: new Date().toISOString(),
      metrics,
      progress,
      patterns
    };
    
    return JSON.stringify(exportData, null, 2);
  }
}

/**
 * Istanza singleton del servizio
 */
export const trainingAnalytics = new TrainingAnalyticsService();
